﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class NotificationPreferenceService : INotificationPreferenceService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public NotificationPreferenceService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<NotificationPreferenceDto> GetPreferencesByUserIdAsync(Guid userId)
        {
            var preferences = (await _unitOfWork.NotificationPreferences.FindAsync(p => p.UserId == userId)).FirstOrDefault();
            if (preferences == null)
            {
                // Create default preferences if not found
                preferences = new NotificationPreference { UserId = userId };
                await _unitOfWork.NotificationPreferences.AddAsync(preferences);
                await _unitOfWork.SaveChangesAsync();
            }

            return _mapper.Map<NotificationPreferenceDto>(preferences);
        }

        public async Task<NotificationPreferenceDto> UpdatePreferencesAsync(Guid userId, NotificationPreferenceDto preferencesDto)
        {
            var preferences = (await _unitOfWork.NotificationPreferences.FindAsync(p => p.UserId == userId)).FirstOrDefault();
            if (preferences == null)
            {
                preferences = _mapper.Map<NotificationPreference>(preferencesDto);
                preferences.UserId = userId;
                await _unitOfWork.NotificationPreferences.AddAsync(preferences);
            }
            else
            {
                _mapper.Map(preferencesDto, preferences);
                _unitOfWork.NotificationPreferences.Update(preferences);
            }

            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<NotificationPreferenceDto>(preferences);
        }
    }

}
