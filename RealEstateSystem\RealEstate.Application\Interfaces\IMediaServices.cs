﻿using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IMediaServices
    {
        Task<IEnumerable<PropertyMediaDto>> GetAllMediaAsync(List<Guid> mediaIds);
        Task<PropertyMediaDto> GetMediaByIdAsync(Guid id);
        Task<IEnumerable<PropertyMediaDto>> GetAllMediaAsync();
        Task<IEnumerable<PropertyMediaDto>> GetMediaByPropertyIdAsync(Guid propertyId);
        Task<PropertyMediaDto> CreateMediaAsync(CreateMediaDto mediaDto);
        Task<PropertyMediaDto> UpdateMediaAsync(Guid id, CreateMediaDto mediaDto);
        Task<List<Guid>> UpdateMediaAsync(Guid propertyId, List<Guid> mediaIds);
        Task<Guid> UpdateMediaAsync(Guid mediaId, Guid propertyId, string filePath, PropertyMediaDto propertyMedia);
        Task<bool> DeleteMediaAsync(Guid id);
    }
}
