﻿using Microsoft.AspNetCore.Authorization;
using RealEstate.Application.Interfaces;
using System.Security.Claims;

namespace RealEstate.API.CustomAuthorizationPolicy
{   
    public class MustBePropertyOwnerRequirement : IAuthorizationRequirement { }
    public class MustBePropertyOwnerHandler : AuthorizationHandler<MustBePropertyOwnerRequirement>
    {
        private readonly IPropertyService _propertyService;

        public MustBePropertyOwnerHandler(IPropertyService propertyService)
        {
            _propertyService = propertyService;
        }

        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, MustBePropertyOwnerRequirement requirement)
        {
            var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var routeData = (context.Resource as Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)?.RouteData;

            if (userIdClaim == null || !Guid.TryParse(userIdClaim, out Guid userId))
            {
                context.Fail();  // Invalid user
                return;
            }

            if (routeData?.Values["propertyId"] == null || !Guid.TryParse(routeData.Values["propertyId"].ToString(), out Guid propertyId))
            {
                context.Fail();  // Invalid property ID
                return;
            }

            var property = await _propertyService.GetPropertyByIdAsync(propertyId);

            if (property != null && property.OwnerId == userId)
            {
                context.Succeed(requirement);  // ✅ User owns the property
            }
            else
            {
                context.Fail();  // ❌ User does not own the property
            }
        }
    }
}
