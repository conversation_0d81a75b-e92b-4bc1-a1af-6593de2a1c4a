﻿
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using RealEstate.Domain.Common;

namespace RealEstate.Application.Services
{
    public class TokenService : ITokenService
    {
        private readonly IConfiguration _config;
        private readonly SymmetricSecurityKey _key;

        public TokenService(IConfiguration config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            var tokenKey = _config["JWT:Key"] ??
                throw new InvalidOperationException("Token:Key is not configured in appsettings.json");

            _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(tokenKey));
        }

        public string CreateToken(AppUser user)
        {
            if (user == null) throw new ArgumentNullException(nameof(user));
            if (string.IsNullOrEmpty(user.Email)) throw new ArgumentException("User email is required");
            if (string.IsNullOrEmpty(user.FullName)) throw new ArgumentException("Username is required");
            if (string.IsNullOrEmpty(user.UserType)) throw new ArgumentException("User type is required");

            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Name, user.FullName),
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            };

            if (user.UserType == EnumValues.UserType.Admin.ToString())
            {
                // Nếu là Admin, thêm các vai trò và quyền từ AdminRole/RolePermission
                if (user.UserRoles != null) // UserRoles PHẢI được eager-load hoặc explicit-load cho Admin
                {
                    var uniquePermissionCodes = new HashSet<string>();

                    foreach (var userRole in user.UserRoles)
                    {
                        if (userRole?.Role?.Code != null)
                        {
                            // Thêm vai trò vào claims (sử dụng Code của vai trò)
                            claims.Add(new Claim(ClaimTypes.Role, userRole.Role.Code));

                            // Nạp và thêm quyền vào claims
                            if (userRole.Role.RolePermissions != null) // RolePermissions PHẢI được eager-load
                            {
                                foreach (var rolePermission in userRole.Role.RolePermissions)
                                {
                                    if (rolePermission.Permission?.Code != null)
                                    {
                                        uniquePermissionCodes.Add(rolePermission.Permission.Code);
                                    }
                                }
                            }
                        }
                    }
                    // Thêm tất cả các quyền duy nhất vào claims
                    foreach (var permCode in uniquePermissionCodes)
                    {
                        claims.Add(new Claim("permission_code", permCode));
                    }
                }
                else
                {
                    // Nếu UserType là Admin nhưng không có UserRoles nào được gán,
                    // có thể ném exception hoặc thêm một vai trò "Admin Default" không có quyền.
                    // Điều này phụ thuộc vào quy tắc nghiệp vụ của bạn.
                    // Hiện tại, tôi sẽ không ném exception ở đây, mà để logic phân quyền xử lý.
                    // Cần đảm bảo rằng Admin luôn được gán ít nhất một AdminRole.
                    claims.Add(new Claim(ClaimTypes.Role, EnumValues.UserType.Admin.ToString())); // Thêm UserType làm vai trò chung
                }
            }
            else // Đối với Buyer hoặc Seller
            {
                // Chỉ thêm UserType làm vai trò
                claims.Add(new Claim(ClaimTypes.Role, user.UserType.ToString()));
                // Đối với Buyer/Seller, không cần thêm permission_code claims
                // vì các API của họ chỉ yêu cầu UserExists và RoleType (Buyer/Seller)
            }

            var creds = new SigningCredentials(_key, SecurityAlgorithms.HmacSha256Signature);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddDays(1),
                SigningCredentials = creds,
                Issuer = _config["JWT:Issuer"],
                Audience = _config["JWT:Audience"]
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);

            return tokenHandler.WriteToken(token);
        }

        public string GenerateRefreshToken()
        {
            return Guid.NewGuid().ToString();
        }
    }
}
