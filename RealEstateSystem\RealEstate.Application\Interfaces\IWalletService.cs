using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Wallet;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RealEstate.Application.Interfaces
{
    public interface IWalletService
    {
        Task<WalletBalanceDto> GetWalletBalanceAsync(Guid userId);
        Task<WalletTransactionDto> TopUpWalletAsync(Guid userId, TopUpWalletDto request);
        Task<WalletTransactionDto> SpendFromWalletAsync(Guid userId, SpendWalletDto request);
        Task<IEnumerable<WalletTransactionDto>> GetWalletTransactionsAsync(Guid userId, int page = 1, int pageSize = 20);
        Task<WalletTransactionDto> GetTransactionByIdAsync(Guid transactionId, Guid userId);
        Task<WalletTransactionDto> ProcessTransactionAsync(Guid transactionId, ProcessTransactionDto request, Guid processedByUserId);
        Task<IEnumerable<WalletTransactionDto>> GetPendingTransactionsAsync(int page = 1, int pageSize = 20);
        Task<IEnumerable<WalletTransactionDto>> GetUserPendingTransactionsAsync(Guid userId, int page = 1, int pageSize = 20);
        Task<TransactionSearchResultDto> SearchTransactionsAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null);
        Task<byte[]> ExportTransactionsToExcelAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null);
    }
}