﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.DTO.UserFavorite;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.AutoMapperProfiles
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // AdminRole
            CreateMap<AdminRole, AdminRoleDto>().ReverseMap();
            CreateMap<AdminRole, CreateAdminRoleDto>();

            // User
            CreateMap<AppUser, UserDto>()
                .ForMember(dest => dest.AvatarImage, opt => opt.MapFrom(src => src.AvatarImage))
                .ForMember(dest => dest.PersonalTaxCode, opt => opt.MapFrom(src => src.PersonalTaxCode))
                .ForMember(dest => dest.InvoiceInfo, opt => opt.Ignore()) // Handled manually in service
                .ReverseMap();
            CreateMap<CreateUserDto, AppUser>()
                .ForMember(dest => dest.PasswordHash, opt => opt.Ignore())
                .ForMember(dest => dest.PasswordSalt, opt => opt.Ignore());

            // User Invoice Info
            CreateMap<UserInvoiceInfoDto, AppUser>()
                .ForMember(dest => dest.InvoiceBuyerName, opt => opt.MapFrom(src => src.BuyerName))
                .ForMember(dest => dest.InvoiceEmail, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.InvoiceCompanyName, opt => opt.MapFrom(src => src.CompanyName))
                .ForMember(dest => dest.InvoiceTaxCode, opt => opt.MapFrom(src => src.TaxCode))
                .ForMember(dest => dest.InvoiceAddress, opt => opt.MapFrom(src => src.Address));

            CreateMap<AppUser, UserInvoiceInfoDto>()
                .ForMember(dest => dest.BuyerName, opt => opt.MapFrom(src => src.InvoiceBuyerName))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.InvoiceEmail))
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.InvoiceCompanyName))
                .ForMember(dest => dest.TaxCode, opt => opt.MapFrom(src => src.InvoiceTaxCode))
                .ForMember(dest => dest.Address, opt => opt.MapFrom(src => src.InvoiceAddress));

            CreateMap<AddUserRoleDto, UserRole>()
                .ForMember(dest => dest.UserID, opt => opt.MapFrom(src => src.UserId))
                .ForMember(dest => dest.RoleID, opt => opt.MapFrom(src => src.RoleId));

            CreateMap<UpdateUserAvatarDto, AppUser>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.UserId))
                .ForMember(dest => dest.AvatarImage, opt => opt.MapFrom(src => src.AvatarImage));

            // Property
            CreateMap<Property, PropertyDto>().ReverseMap();
            CreateMap<CreatePropertyDto, Property>();

            // Property Media
            CreateMap<PropertyMedia, PropertyMediaDto>().ReverseMap();
            CreateMap<CreateMediaDto, PropertyMedia>();

            // User Avatar
            CreateMap<UserAvatar, UserAvatarDto>().ReverseMap();
            CreateMap<CreateUserAvatarDto, UserAvatar>();

            // Property Review
            CreateMap<PropertyReview, PropertyReviewDto>().ReverseMap();
            CreateMap<CreatePropertyReviewDto, PropertyReview>();

            // Owner Review
            CreateMap<OwnerReview, OwnerReviewDto>().ReverseMap();
            CreateMap<CreateOwnerReviewDto, OwnerReview>();

            // Blog Post
            CreateMap<BlogPost, BlogPostDto>()
                .ForMember(dest => dest.AuthorName, opt => opt.MapFrom(src => src.Author.FullName))
                .ForMember(dest => dest.BlogComments, opt => opt.MapFrom(src => src.BlogComments))
                .ReverseMap();
            CreateMap<CreateBlogPostDto, BlogPost>();

            // Blog Comment
            CreateMap<BlogComment, BlogCommentDto>().ReverseMap();
            CreateMap<CreateBlogCommentDto, BlogComment>();

            // Property Status Log
            CreateMap<PropertyStatusLog, PropertyStatusLogDto>().ReverseMap();

            // User Favorite
            CreateMap<UserFavorite, UserFavoriteDto>().ReverseMap();
            CreateMap<CreateUserFavoriteDto, UserFavorite>()
                .ForMember(dest => dest.UserID, opt => opt.Ignore())  // UserID is set from context
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow));

            // City
            CreateMap<City, CityDto>().ReverseMap();

            // District
            CreateMap<District, DistrictDto>().ReverseMap();

            // Ward
            CreateMap<Ward, WardDto>().ReverseMap();

            // Street
            CreateMap<Street, StreetDto>().ReverseMap();

            // Project
            CreateMap<Project, ProjectDto>().ReverseMap();

            // ContactRequest
            // Mapping từ Entity -> DTO
            CreateMap<ContactRequest, ContactRequestDto>();

            // Mapping từ DTO -> Entity
            CreateMap<CreateContactRequestDto, ContactRequest>();

            // Mapping từ Update DTO -> Entity
            CreateMap<UpdateContactRequestDto, ContactRequest>()
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

            // wallet
            CreateMap<Wallet, WalletDto>().ReverseMap();

            // wallet transaction
            CreateMap<WalletTransaction, WalletTransactionDto>().ReverseMap();

            // member ranking
            CreateMap<MemberRanking, MemberRankingDto>().ReverseMap();

            // highlight fee
            CreateMap<HighlightFee, HighlightFeeDto>().ReverseMap();

            // Mapping for Notification -> NotificationDto
            CreateMap<Notification, NotificationDto>().ReverseMap();

            // Mapping for NotificationPreference -> NotificationPreferenceDto
            CreateMap<NotificationPreference, NotificationPreferenceDto>().ReverseMap();

            // Mapping for WalletTransaction -> WalletTransactionDto
            CreateMap<WalletTransaction, WalletTransactionDto>();

            CreateMap<Notification, NotificationDto>();
            CreateMap<CreateNotificationDto, Notification>();

            // Permission System
            CreateMap<Permission, PermissionDto>().ReverseMap();
            CreateMap<CreatePermissionDto, Permission>();

            CreateMap<RolePermission, RolePermissionDto>().ReverseMap();
            CreateMap<CreateRolePermissionDto, RolePermission>();

            // Invoice System
            CreateMap<Invoice, InvoiceDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ? src.User.FullName : null))
                .ForMember(dest => dest.UserEmail, opt => opt.MapFrom(src => src.User != null ? src.User.Email : null))
                .ForMember(dest => dest.PropertyTitle, opt => opt.MapFrom(src => src.Property != null ? src.Property.Name : null))
                .ForMember(dest => dest.PropertyAddress, opt => opt.MapFrom(src => src.Property != null ? src.Property.Address : null));

            CreateMap<CreateInvoiceDto, Invoice>()
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => FunctionHelper.GetEnumDescription(src.Type)))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(_ => FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING)))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow))
                .ForMember(dest => dest.UserId, opt => opt.Ignore())
                .ForMember(dest => dest.InvoiceItems, opt => opt.Ignore());

            CreateMap<InvoiceItem, InvoiceItemDto>().ReverseMap();

            CreateMap<CreateInvoiceItemDto, InvoiceItem>()
                .ForMember(dest => dest.ItemType, opt => opt.MapFrom(src => FunctionHelper.GetEnumDescription(src.ItemType)))
                .ForMember(dest => dest.InvoiceId, opt => opt.Ignore());

        }
    }
}
