﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.UserFavorite;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RealEstate.Application.Services
{
    public class UserFavoriteService : IUserFavoriteService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IPropertyAnalyticsService _propertyAnalyticsService;

        public UserFavoriteService(IUnitOfWork unitOfWork, IMapper mapper, IPropertyAnalyticsService propertyAnalyticsService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _propertyAnalyticsService = propertyAnalyticsService;
        }

        public async Task<IEnumerable<UserFavoriteDto>> GetUserFavoritesAsync(Guid userId)
        {
            var favorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => f.UserID == userId)
                .ToListAsync();

            return _mapper.Map<IEnumerable<UserFavoriteDto>>(favorites);
        }

        public async Task<PagedFavoriteResultDto> GetUserFavoritesWithDetailsAsync(Guid userId, FavoriteFilterDto filter)
        {
            var query = _unitOfWork.UserFavorites.GetQueryable()
                .Include(f => f.Property)
                .ThenInclude(p => p.PropertyMedia)
                .Where(f => f.UserID == userId && !f.Property.IsDeleted);

            // Apply price filters
            if (filter.MinPrice.HasValue)
                query = query.Where(f => f.Property.Price >= filter.MinPrice.Value);

            if (filter.MaxPrice.HasValue)
                query = query.Where(f => f.Property.Price <= filter.MaxPrice.Value);

            // Apply date filters
            if (filter.FromDate.HasValue)
                query = query.Where(f => f.CreatedAt >= filter.FromDate.Value);

            if (filter.ToDate.HasValue)
                query = query.Where(f => f.CreatedAt <= filter.ToDate.Value);

            // Apply sorting
            switch (filter.SortBy?.ToLower())
            {
                case "price":
                    query = filter.SortDescending
                        ? query.OrderByDescending(f => f.Property.Price)
                        : query.OrderBy(f => f.Property.Price);
                    break;
                case "createdat":
                default:
                    query = filter.SortDescending
                        ? query.OrderByDescending(f => f.CreatedAt)
                        : query.OrderBy(f => f.CreatedAt);
                    break;
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Calculate pagination
            var pageCount = (int)Math.Ceiling(totalCount / (double)filter.PageSize);
            var hasNextPage = filter.Page < pageCount;
            var hasPreviousPage = filter.Page > 1;

            // Apply pagination
            var favorites = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Map to DTOs
            var favoriteWithPropertyDtos = favorites.Select(f => new FavoriteWithPropertyDto
            {
                Id = f.Id,
                PropertyId = f.PropertyID,
                CreatedAt = f.CreatedAt,
                Property = _mapper.Map<PropertyDto>(f.Property)
            });

            return new PagedFavoriteResultDto
            {
                Items = favoriteWithPropertyDtos,
                TotalCount = totalCount,
                CurrentPage = filter.Page,
                PageSize = filter.PageSize,
                PageCount = pageCount,
                HasNextPage = hasNextPage,
                HasPreviousPage = hasPreviousPage
            };
        }

        public async Task<bool> AddToFavoritesAsync(Guid userId, Guid propertyId)
        {
            // Check if already favorited
            var existingFavorite = await _unitOfWork.UserFavorites.GetQueryable()
                .FirstOrDefaultAsync(f => f.UserID == userId && f.PropertyID == propertyId);

            if (existingFavorite != null)
            {
                // Already favorited
                return true;
            }

            // Verify property exists
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null || property.IsDeleted)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found");
            }

            // Verify user exists
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Create new favorite
            var favorite = new UserFavorite
            {
                UserID = userId,
                PropertyID = propertyId,
                CreatedAt = DateTime.UtcNow,
            };

            await _unitOfWork.UserFavorites.AddAsync(favorite);
            await _unitOfWork.SaveChangesAsync();

            // Update property engagement summary
            try
            {
                await _propertyAnalyticsService.UpdatePropertyEngagementSummaryAsync(propertyId);
            }
            catch (Exception)
            {
                // Log the error but don't fail the request
            }

            return true;
        }

        public async Task<bool> RemoveFromFavoritesAsync(Guid userId, Guid propertyId)
        {
            var favorite = await _unitOfWork.UserFavorites.GetQueryable()
                .FirstOrDefaultAsync(f => f.UserID == userId && f.PropertyID == propertyId);

            if (favorite == null)
            {
                // Not found, consider it already removed
                return true;
            }

            _unitOfWork.UserFavorites.Remove(favorite);
            await _unitOfWork.SaveChangesAsync();

            // Update property engagement summary
            try
            {
                await _propertyAnalyticsService.UpdatePropertyEngagementSummaryAsync(propertyId);
            }
            catch (Exception)
            {
                // Log the error but don't fail the request
            }

            return true;
        }

        public async Task<List<FavoriteStatusDto>> CheckFavoriteStatusAsync(Guid userId, List<Guid> propertyIds)
        {
            var userFavorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => f.UserID == userId && propertyIds.Contains(f.PropertyID))
                .Select(f => f.PropertyID)
                .ToListAsync();

            var result = propertyIds.Select(id => new FavoriteStatusDto
            {
                PropertyId = id,
                IsFavorite = userFavorites.Contains(id)
            }).ToList();

            return result;
        }

        public async Task<int> GetFavoritesCountAsync(Guid userId)
        {
            return await _unitOfWork.UserFavorites.GetQueryable()
                .CountAsync(f => f.UserID == userId);
        }
    }
}
