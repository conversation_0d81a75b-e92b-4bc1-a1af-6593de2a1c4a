﻿using RealEstate.Domain.CustomModel;
using System.Linq.Expressions;

namespace RealEstate.Domain.Interfaces
{
    public interface IRepository<T> where T : IEntity
    {
        Task<IEnumerable<T>> GetAllAsync(bool asNoTracking = true, params Expression<Func<T, object>>[] includes);
        Task<T> GetByIdAsync(Guid id, bool asNoTracking = true, params Expression<Func<T, object>>[] includes);
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, bool asNoTracking = true, params Expression<Func<T, object>>[] includes);
        Task<PagedResult<T>> GetPagedAsync(PagingRequest paging, Expression<Func<T, bool>> predicate = null, bool asNoTracking = true, params Expression<Func<T, object>>[] includes);
        Task AddAsync(T entity);
        void Update(T entity);
        void Remove(T entity);
        void RestoreAsync(T entity);

        /// <summary>
        /// Gets a queryable collection of all entities
        /// </summary>
        /// <returns>IQueryable of entities</returns>
        IQueryable<T> GetQueryable();
    }
}
