using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class PropertyStatusLog : BaseEntity
    {
        public Guid PropertyID { get; set; }
        public string? Status { get; set; }
        public Guid ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public string? Comment { get; set; }

        [ForeignKey("PropertyID")]
        public Property? Property { get; set; }

        [ForeignKey("ChangedBy")]
        public AppUser? ChangedByUser { get; set; }
    }
}