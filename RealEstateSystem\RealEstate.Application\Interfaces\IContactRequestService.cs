﻿using RealEstate.Application.DTO;
using RealEstate.Domain.CustomModel;

namespace RealEstate.Application.Interfaces
{
    public interface IContactRequestService
    {
        Task<ContactRequestDto> GetByIdAsync(Guid id);
        Task<IEnumerable<ContactRequestDto>> GetAllAsync();
        Task<PagedResult<ContactRequestDto>> GetPagedAsync(PagingRequest paging, string? phone);
        Task<IEnumerable<ContactRequestDto>> GetByPropertyIdAsync(Guid propertyId);
        Task<ContactRequestDto> CreateAsync(CreateContactRequestDto requestDto);
        Task<bool> UpdateAsync(Guid id, UpdateContactRequestDto requestDto);
        Task<bool> DeleteAsync(Guid id);
    }
}
