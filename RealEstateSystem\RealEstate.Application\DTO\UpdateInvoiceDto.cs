using System.ComponentModel.DataAnnotations;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class UpdateInvoiceDto
    {
        public InvoiceType? Type { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "Total amount must be greater than or equal to 0")]
        public int? TotalAmount { get; set; }
        
        public InvoiceStatus? Status { get; set; }
        
        public string? Note { get; set; }
    }
}
