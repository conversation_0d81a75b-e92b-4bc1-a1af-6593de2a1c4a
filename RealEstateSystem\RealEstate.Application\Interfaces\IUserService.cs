﻿using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IUserService
    {
        Task<bool> IsUserExistsAsync(Guid userId);
        Task<bool> IsUserAdminExistsAsync(Guid userId);
        Task<UserDto> GetUserByIdAsync(Guid id);
        Task<UserDto> GetUserByEmailAsync(string email);
        Task<IEnumerable<UserDto>> GetAllUsersAsync();
        Task<UserDto> CreateUserAsync(CreateUserDto userDto);
        Task<bool> UpdateUserAsync(Guid id, CreateUserDto userDto);
        Task<bool> DeleteUserAsync(Guid id);
        Task<bool> AddUserRoleAsync(AddUserRoleDto addUserRoleDto);
        Task<bool> UpdateUserAvatarAsync(Guid userId, string avatarImage);
        Task<string> GetUserAvatarImageAsync(Guid userId);
        Task<bool> DeactivateUserAsync(Guid userId, DeactivateUserDto deactivateUserDto);
        Task<bool> ReactivateUserAsync(Guid userId);
        Task<bool> PermanentDeleteUserAsync(Guid userId, DeactivateUserDto deactivateUserDto);
        Task<bool> UpdateUserTaxInfoAsync(Guid userId, UpdateUserTaxInfoDto taxInfoDto);
        Task<UserInvoiceInfoDto> GetUserInvoiceInfoAsync(Guid userId);

        // Permission-related methods
        Task<IEnumerable<PermissionDto>> GetUserPermissionsAsync(Guid userId);
        Task<bool> UserHasPermissionAsync(Guid userId, string permissionCode);
        Task<IEnumerable<AdminRoleDto>> GetUserRolesAsync(Guid userId);
    }
}
