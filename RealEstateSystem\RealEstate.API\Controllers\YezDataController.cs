using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class YezDataController : ControllerBase
    {
        private readonly IYezDataService _yezDataService;
        private readonly ILogger<YezDataController> _logger;
        private readonly IMemoryCache _memoryCache;
        private const string HighlightFeesCacheKey = "HighlightFees";
        private const string MemberRankingsCacheKey = "MemberRankings";
        private static readonly TimeSpan CacheExpiration = TimeSpan.FromHours(1);

        public YezDataController(IYezDataService yezDataService, ILogger<YezDataController> logger, IMemoryCache memoryCache)
        {
            _yezDataService = yezDataService;
            _logger = logger;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Get all highlight fees
        /// </summary>
        /// <returns>List of highlight fees for each membership rank</returns>
        /// <response code="200">Returns the list of highlight fees</response>
        [HttpGet("highlight-fees")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<HighlightFeeDto>>> GetHighlightFees()
        {
            try
            {
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                _logger.LogInformation("Retrieving all highlight fees from IP {IpAddress}", ipAddress);

                if (_memoryCache.TryGetValue(HighlightFeesCacheKey, out IEnumerable<HighlightFeeDto> cachedFees))
                {
                    _logger.LogInformation("Retrieved {Count} highlight fees from cache", cachedFees?.Count() ?? 0);
                    return Ok(cachedFees);
                }

                var highlightFees = await _yezDataService.GetAllHighlightFeesAsync();
                
                if (highlightFees != null)
                {
                    _memoryCache.Set(HighlightFeesCacheKey, highlightFees, CacheExpiration);
                }

                _logger.LogInformation("Successfully retrieved {Count} highlight fees from service", highlightFees?.Count() ?? 0);
                return Ok(highlightFees);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving highlight fees");
                return StatusCode(500, new { Message = "An error occurred while retrieving highlight fees" });
            }
        }

        /// <summary>
        /// Get highlight fee by rank name
        /// </summary>
        /// <param name="rankName">The name of the membership rank</param>
        /// <returns>The highlight fee for the specified rank</returns>
        /// <response code="200">Returns the highlight fee</response>
        /// <response code="404">If the rank is not found</response>
        [HttpGet("highlight-fees/{rankName}")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HighlightFeeDto>> GetHighlightFeeByRank(string rankName)
        {
            try
            {
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                _logger.LogInformation("Retrieving highlight fee for rank {RankName} from IP {IpAddress}", rankName, ipAddress);

                string cacheKey = $"{HighlightFeesCacheKey}_{rankName}";
                if (_memoryCache.TryGetValue(cacheKey, out HighlightFeeDto cachedFee))
                {
                    _logger.LogInformation("Retrieved highlight fee for rank {RankName} from cache", rankName);
                    return Ok(cachedFee);
                }

                var highlightFeeDto = await _yezDataService.GetHighlightFeeByRankAsync(rankName);

                if (highlightFeeDto == null)
                {
                    _logger.LogWarning("Highlight fee for rank {RankName} not found", rankName);
                    return NotFound(new { Message = $"Highlight fee for rank '{rankName}' not found" });
                }

                _memoryCache.Set(cacheKey, highlightFeeDto, CacheExpiration);
                _logger.LogInformation("Successfully retrieved highlight fee for rank {RankName}", rankName);
                return Ok(highlightFeeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving highlight fee for rank {RankName}", rankName);
                return StatusCode(500, new { Message = $"An error occurred while retrieving highlight fee for rank '{rankName}'" });
            }
        }

        /// <summary>
        /// Get all member rankings
        /// </summary>
        /// <returns>List of member rankings</returns>
        /// <response code="200">Returns the list of member rankings</response>
        [HttpGet("member-rankings")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<MemberRankListDto>>> GetMemberRankings()
        {
            try
            {
                if (_memoryCache.TryGetValue(MemberRankingsCacheKey, out IEnumerable<MemberRankListDto> cachedRankings))
                {
                    return Ok(cachedRankings);
                }

                var memberRankings = await _yezDataService.GetAllMemberRankingsAsync();
                
                if (memberRankings != null)
                {
                    _memoryCache.Set(MemberRankingsCacheKey, memberRankings, CacheExpiration);
                }

                return Ok(memberRankings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving member rankings");
                return StatusCode(500, new { Message = "An error occurred while retrieving member rankings" });
            }
        }

        /// <summary>
        /// Get member ranking by rank name
        /// </summary>
        /// <param name="rankName">The name of the membership rank</param>
        /// <returns>The member ranking for the specified rank</returns>
        /// <response code="200">Returns the member ranking</response>
        /// <response code="404">If the rank is not found</response>
        [HttpGet("member-rankings/{rankName}")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<MemberRankListDto>> GetMemberRankingByName(string rankName)
        {
            try
            {
                string cacheKey = $"{MemberRankingsCacheKey}_{rankName}";
                if (_memoryCache.TryGetValue(cacheKey, out MemberRankListDto cachedRanking))
                {
                    return Ok(cachedRanking);
                }

                var memberRankingDto = await _yezDataService.GetMemberRankingByNameAsync(rankName);

                if (memberRankingDto == null)
                {
                    return NotFound($"Member ranking '{rankName}' not found");
                }

                _memoryCache.Set(cacheKey, memberRankingDto, CacheExpiration);
                return Ok(memberRankingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving member ranking {RankName}", rankName);
                return StatusCode(500, new { Message = $"An error occurred while retrieving member ranking '{rankName}'" });
            }
        }
    }
}
