﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("[controller]")]
    [Authorize]
    public class MediaController : Controller
    {
        private readonly IMediaServices _mediaServices;
        private readonly ILogger<MediaController> _logger;

        public MediaController(IMediaServices mediaServices, ILogger<MediaController> logger)
        {
            _mediaServices = mediaServices;
            _logger = logger;
        }

        [HttpGet("{fileId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetMedia(Guid fileId, [FromQuery] string size = null)
        {
            try
            {
                _logger.LogInformation("Retrieving media {FileId} with size {Size} from IP {IpAddress}",
                    fileId, size ?? "original", HttpContext.Connection.RemoteIpAddress?.ToString());

                var media = await _mediaServices.GetMediaByIdAsync(fileId);
                if (media == null)
                {
                    _logger.LogWarning("Media {FileId} not found", fileId);
                    return NotFound(new { Message = "Media not found" });
                }

                string filePath = string.Empty;

                // Choose the appropriate file path based on the requested size
                switch (size?.ToLowerInvariant())
                {
                    case "thumbnail":
                        filePath = !string.IsNullOrEmpty(media.ThumbnailURL) ? media.ThumbnailURL! : media.FilePath!;
                        break;
                    case "small":
                        filePath = !string.IsNullOrEmpty(media.SmallURL) ? media.SmallURL! : media.FilePath!;
                        break;
                    case "medium":
                        filePath = !string.IsNullOrEmpty(media.MediumURL) ? media.MediumURL! : media.FilePath!;
                        break;
                    case "large":
                        filePath = !string.IsNullOrEmpty(media.LargeURL) ? media.LargeURL! : media.FilePath!;
                        break;
                    default:
                        filePath = media.FilePath!;
                        break;
                }

                if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("Media file not found on disk for media {FileId}, path: {FilePath}", fileId, filePath);
                    return NotFound(new { Message = "Media file not found" });
                }

                _logger.LogInformation("Successfully serving media {FileId} from path {FilePath}", fileId, filePath);
                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                return File(fileStream, media.MediaType!);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving media {FileId}", fileId);
                return StatusCode(500, new { Message = "An error occurred while retrieving media. Please try again later." });
            }
        }

        [HttpPut("update-caption")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateCaption([FromBody] UpdatePropertyMediaCaptionDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for updating media caption {MediaId}: {ValidationErrors}",
                        updateDto.Id, ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Updating caption for media {MediaId} by user {UserId}", updateDto.Id, HttpContext.User.Identity?.Name);

                var media = await _mediaServices.GetMediaByIdAsync(updateDto.Id);
                if (media == null)
                {
                    _logger.LogWarning("Media {MediaId} not found for caption update", updateDto.Id);
                    return NotFound(new { Message = "Media not found" });
                }

                // Update the media with the new caption
                var mediaToUpdate = new CreateMediaDto
                {
                    Id = media.Id,
                    PropertyID = media.PropertyID,
                    MediaType = media.MediaType,
                    MediaURL = media.MediaURL,
                    FilePath = media.FilePath,
                    ThumbnailPath = media.ThumbnailURL,
                    SmallPath = media.SmallURL,
                    MediumPath = media.MediumURL,
                    LargePath = media.LargeURL,
                    UploadedAt = media.UploadedAt,
                    IsAvatar = media.IsAvatar,
                    Caption = updateDto.Caption // Update the caption
                };

                var updatedMedia = await _mediaServices.UpdateMediaAsync(updateDto.Id, mediaToUpdate);

                _logger.LogInformation("Successfully updated caption for media {MediaId}", updateDto.Id);
                return Ok(updatedMedia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating caption for media {MediaId}", updateDto.Id);
                return StatusCode(500, new { Message = "An error occurred while updating media caption. Please try again later." });
            }
        }

        [HttpPut("update-is-avatar")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateIsAvatar([FromBody] UpdatePropertyMediaIsAvatarDto updateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var media = await _mediaServices.GetMediaByIdAsync(updateDto.Id);
            if (media == null) return NotFound("Media not found");

            if (media.PropertyID == null)
            {
                return BadRequest("This media is not associated with any property");
            }

            // If setting as avatar, first reset all other media for this property
            if (updateDto.IsAvatar)
            {
                var propertyMedia = await _mediaServices.GetMediaByPropertyIdAsync(media.PropertyID.Value);
                foreach (var item in propertyMedia)
                {
                    if (item.Id != updateDto.Id && item.IsAvatar)
                    {
                        // Reset IsAvatar for other media
                        var resetMedia = new CreateMediaDto
                        {
                            Id = item.Id,
                            PropertyID = item.PropertyID,
                            MediaType = item.MediaType,
                            MediaURL = item.MediaURL,
                            FilePath = item.FilePath,
                            ThumbnailPath = item.ThumbnailURL,
                            SmallPath = item.SmallURL,
                            MediumPath = item.MediumURL,
                            LargePath = item.LargeURL,
                            UploadedAt = item.UploadedAt,
                            Caption = item.Caption,
                            IsAvatar = false // Reset to false
                        };

                        await _mediaServices.UpdateMediaAsync(item.Id, resetMedia);
                    }
                }
            }

            // Update the target media with the new IsAvatar value
            var mediaToUpdate = new CreateMediaDto
            {
                Id = media.Id,
                PropertyID = media.PropertyID,
                MediaType = media.MediaType,
                MediaURL = media.MediaURL,
                FilePath = media.FilePath,
                ThumbnailPath = media.ThumbnailURL,
                SmallPath = media.SmallURL,
                MediumPath = media.MediumURL,
                LargePath = media.LargeURL,
                UploadedAt = media.UploadedAt,
                Caption = media.Caption,
                IsAvatar = updateDto.IsAvatar // Update IsAvatar
            };

            var updatedMedia = await _mediaServices.UpdateMediaAsync(updateDto.Id, mediaToUpdate);
            return Ok(updatedMedia);
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> DeleteMedia(Guid id)
        {
            var media = await _mediaServices.GetMediaByIdAsync(id);
            if (media == null) return NotFound("Media not found");

            // Store file paths before deleting from database
            var filePaths = new List<string>();
            if (!string.IsNullOrEmpty(media.FilePath) && System.IO.File.Exists(media.FilePath))
                filePaths.Add(media.FilePath);
            if (!string.IsNullOrEmpty(media.ThumbnailURL) && System.IO.File.Exists(media.ThumbnailURL))
                filePaths.Add(media.ThumbnailURL);
            if (!string.IsNullOrEmpty(media.SmallURL) && System.IO.File.Exists(media.SmallURL))
                filePaths.Add(media.SmallURL);
            if (!string.IsNullOrEmpty(media.MediumURL) && System.IO.File.Exists(media.MediumURL))
                filePaths.Add(media.MediumURL);
            if (!string.IsNullOrEmpty(media.LargeURL) && System.IO.File.Exists(media.LargeURL))
                filePaths.Add(media.LargeURL);

            // Delete from database
            var result = await _mediaServices.DeleteMediaAsync(id);
            if (!result) return BadRequest("Failed to delete media from database");

            // Delete physical files
            foreach (var path in filePaths)
            {
                try
                {
                    System.IO.File.Delete(path);
                    _logger.LogInformation("Successfully deleted file: {FilePath}", path);
                }
                catch (Exception ex)
                {
                    // Log error but continue with other files
                    // We don't want to fail the entire operation if one file can't be deleted
                    _logger.LogError(ex, "Failed to delete file: {FilePath}", path);
                }
            }

            return Ok(new { message = "Media deleted successfully" });
        }

    }
}
