﻿using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IAuthService
    {
        Task<UserDto> RegisterAsync(CreateUserDto registerDto);
        Task<UserDto> LoginAsync(LoginDto loginDto);
        Task<UserDto> RefreshToken(Guid userId);
        Task<UserDto> ChangePassword(ChangePasswordDto changePasswordDto);
        Task<bool> ValidateUserCredentialsAsync(string email, string password);
    }
}
