using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.API.Controllers;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System.Security.Claims;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserAvatarController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserAvatarService _userAvatarService;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly ILogger<UserAvatarController> _logger;

        public UserAvatarController(
            IUserService userService,
            IUserAvatarService userAvatarService,
            IImageProcessingService imageProcessingService,
            ILogger<UserAvatarController> logger)
        {
            _userService = userService;
            _userAvatarService = userAvatarService;
            _imageProcessingService = imageProcessingService;
            _logger = logger;
        }

        [HttpPost("upload")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<UserDto>> UploadAvatar(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                _logger.LogWarning("Avatar upload attempt with no file by user {UserId}", GetUserId());
                return BadRequest(new { Message = "No file uploaded." });
            }

            var userId = GetUserId();
            if (userId == null)
            {
                LogSecurityEvent(_logger, "UnauthorizedAvatarUpload", "User attempted to upload avatar without valid authentication");
                return BadRequest(new { Message = "Invalid user" });
            }

            _logger.LogInformation("Avatar upload started for user {UserId} - File: {FileName}, Size: {FileSize} bytes, ContentType: {ContentType}",
                userId.Value, file.FileName, file.Length, file.ContentType);
            LogUserAction(_logger, "UploadAvatar", new { FileName = file.FileName, FileSize = file.Length });

            // Skip non-image files
            if (!file.ContentType.StartsWith("image/"))
            {
                _logger.LogWarning("Invalid file type uploaded by user {UserId}: {ContentType}", userId.Value, file.ContentType);
                return BadRequest(new { Message = "Only image files are allowed." });
            }

            // Generate a unique file ID (UUID-based)
            var fileId = Guid.NewGuid();
            var extension = Path.GetExtension(file.FileName);

            // Create user-specific folder for avatars
            string userFolder = Path.Combine("UserAvatars", userId.Value.ToString());
            if (!Directory.Exists(userFolder))
            {
                Directory.CreateDirectory(userFolder);
            }

            // Define the direct file path
            var filePath = Path.Combine(userFolder, $"{fileId}{extension}");

            // Save the file directly to the user's folder
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            try
            {
                // Generate a public URL
                string publicUrl = $"{Request.Scheme}://{Request.Host}/useravatarmedia/{fileId}";

                // Store metadata in the database
                var userAvatar = new CreateUserAvatarDto
                {
                    Id = fileId,
                    UserID = userId.Value,
                    MediaType = file.ContentType,
                    MediaURL = publicUrl,
                    FilePath = filePath,
                    UploadedAt = DateTime.UtcNow
                };

                await _userAvatarService.CreateUserAvatarAsync(userAvatar);

                _logger.LogInformation("Successfully uploaded avatar for user {UserId} - FileId: {FileId}, Path: {FilePath}",
                    userId.Value, fileId, filePath);

                // Get updated user data
                var updatedUser = await _userService.GetUserByIdAsync(userId.Value);

                return Ok(updatedUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading avatar for user {UserId} - File: {FileName}", GetUserId(), file?.FileName);

                // Clean up the file if it was created but database operation failed
                try
                {
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                        _logger.LogInformation("Cleaned up orphaned avatar file: {FilePath}", filePath);
                    }
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning(cleanupEx, "Failed to clean up orphaned avatar file: {FilePath}", filePath);
                }

                return StatusCode(500, new { Message = "An error occurred while uploading the avatar. Please try again later." });
            }
        }

        [HttpGet]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<UserAvatarDto>> GetAvatarImage()
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    LogSecurityEvent(_logger, "UnauthorizedAvatarAccess", "User attempted to access avatar without valid authentication");
                    return BadRequest(new { Message = "Invalid user" });
                }

                _logger.LogInformation("Retrieving avatar for user {UserId}", userId.Value);
                LogUserAction(_logger, "GetAvatarImage");

                var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(userId.Value);
                if (userAvatar == null)
                {
                    _logger.LogInformation("No avatar found for user {UserId}", userId.Value);
                    return NotFound(new { Message = "No avatar image found for this user." });
                }

                _logger.LogInformation("Successfully retrieved avatar for user {UserId}", userId.Value);
                return Ok(userAvatar);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving avatar for user {UserId}", GetUserId());
                return StatusCode(500, new { Message = "An error occurred while retrieving the avatar. Please try again later." });
            }
        }

        [HttpDelete]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult> RemoveAvatar()
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    LogSecurityEvent(_logger, "UnauthorizedAvatarDeletion", "User attempted to delete avatar without valid authentication");
                    return BadRequest(new { Message = "Invalid user" });
                }

                _logger.LogInformation("Removing avatar for user {UserId}", userId.Value);
                LogUserAction(_logger, "RemoveAvatar");

                // Get the user's avatar
                var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(userId.Value);
                if (userAvatar != null)
                {
                    _logger.LogInformation("Deleting avatar {AvatarId} for user {UserId}", userAvatar.Id, userId.Value);

                    // Delete the avatar
                    await _userAvatarService.DeleteUserAvatarAsync(userAvatar.Id);

                    _logger.LogInformation("Successfully removed avatar for user {UserId}", userId.Value);
                }
                else
                {
                    _logger.LogInformation("No avatar found to remove for user {UserId}", userId.Value);
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing avatar for user {UserId}", GetUserId());
                return StatusCode(500, new { Message = "An error occurred while removing the avatar. Please try again later." });
            }
        }
    }
}
