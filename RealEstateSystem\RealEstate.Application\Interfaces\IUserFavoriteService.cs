﻿using RealEstate.Application.DTO;
using RealEstate.Application.DTO.UserFavorite;

namespace RealEstate.Application.Interfaces
{
    public interface IUserFavoriteService
    {
        Task<IEnumerable<UserFavoriteDto>> GetUserFavoritesAsync(Guid userId);
        Task<PagedFavoriteResultDto> GetUserFavoritesWithDetailsAsync(Guid userId, FavoriteFilterDto filter);
        Task<bool> AddToFavoritesAsync(Guid userId, Guid propertyId);
        Task<bool> RemoveFromFavoritesAsync(Guid userId, Guid propertyId);
        Task<List<FavoriteStatusDto>> CheckFavoriteStatusAsync(Guid userId, List<Guid> propertyIds);
        Task<int> GetFavoritesCountAsync(Guid userId);
    }
}
