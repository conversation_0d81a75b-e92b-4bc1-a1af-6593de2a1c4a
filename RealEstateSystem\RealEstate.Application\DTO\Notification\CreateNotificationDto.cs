namespace RealEstate.Application.DTO.Notification
{
    public class CreateNotificationDto
    {
        public Guid? UserId { get; set; } // Null for system and promotion notifications
        public string? Type { get; set; }
        public string? Title { get; set; }
        public string? Message { get; set; }
        public Guid? RelatedEntityId { get; set; } // Contact request ID, transaction ID, etc.
        public Guid? RelatedPropertyId { get; set; } // Property ID for property-related notifications
        public string? ActionUrl { get; set; } // URL for direct navigation
    }
} 