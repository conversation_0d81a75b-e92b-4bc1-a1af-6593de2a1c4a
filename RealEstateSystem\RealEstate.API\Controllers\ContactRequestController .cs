﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Common;
using RealEstate.Application.DTO.Notification;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ContactRequestController : BaseController
    {
        private readonly IContactRequestService _contactRequestService;
        private readonly INotificationService _notificationService;
        private readonly ILogger<ContactRequestController> _logger;

        public ContactRequestController(
            IContactRequestService contactRequestService,
            INotificationService notificationService,
            ILogger<ContactRequestController> logger)
        {
            _contactRequestService = contactRequestService;
            _notificationService = notificationService;
            _logger = logger;
        }

        [Authorize(Policy = "UserExists")]
        [HttpGet("{id}")]
        public async Task<ActionResult<ContactRequestDto>> GetContactRequestById(Guid id)
        {
            try
            {
                _logger.LogInformation("Retrieving contact request {ContactRequestId} by user {UserId}", id, GetUserId());
                LogUserAction(_logger, "GetContactRequestById", new { ContactRequestId = id });

                var request = await _contactRequestService.GetByIdAsync(id);
                if (request == null)
                {
                    _logger.LogWarning("Contact request {ContactRequestId} not found", id);
                    return NotFound(new { Message = "Contact request not found" });
                }

                _logger.LogInformation("Successfully retrieved contact request {ContactRequestId}", id);
                return Ok(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving contact request {ContactRequestId}", id);
                return StatusCode(500, new { Message = "An error occurred while retrieving the contact request. Please try again later." });
            }
        }

        [HttpGet]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<IEnumerable<ContactRequestDto>>> GetAllContactRequests()
        {
            var result = await _contactRequestService.GetAllAsync();
            return Ok(result);
        }

        [HttpGet("requests")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetContactRequests([FromQuery] PagingRequest request, [FromQuery] string? phone)
        {
            var result = await _contactRequestService.GetPagedAsync(request, phone ?? string.Empty);
            return Ok(result);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<ActionResult<ContactRequestDto>> CreateContactRequest([FromBody] CreateContactRequestDto requestDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for contact request creation from IP {IpAddress}: {ValidationErrors}",
                        GetClientIpAddress(), ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Creating contact request for property {PropertyId} from IP {IpAddress} - Phone: {Phone}",
                    requestDto.PropertyId, GetClientIpAddress(), requestDto.Phone);

                var createdRequest = await _contactRequestService.CreateAsync(requestDto);

                _logger.LogInformation("Successfully created contact request {ContactRequestId} for property {PropertyId}",
                    createdRequest.Id, requestDto.PropertyId);

                // Create notification for property owner
                try
                {
                    var notificationDto = new CreateNotificationDto
                    {
                        UserId = requestDto.PropertyOwnerId,
                        Type = EnumValues.NotificationType.Contact.ToString(),
                        Title = string.Empty,
                        Message = $"Bạn có một yêu cầu liên hệ từ {requestDto.Name} (SĐT: {requestDto.Phone}) cho bài đăng của bạn.",
                        RelatedEntityId = createdRequest.Id, // Contact request ID
                        RelatedPropertyId = requestDto.PropertyId, // Property ID
                        ActionUrl = RealEstate.Application.Services.NotificationService.ConstructActionUrl(
                            EnumValues.NotificationType.Contact.ToString(),
                            requestDto.PropertyId,
                            createdRequest.Id)
                    };

                    await _notificationService.CreateNotificationAsync(notificationDto);

                    _logger.LogInformation("Successfully created notification for property owner {PropertyOwnerId} regarding contact request {ContactRequestId} with property {PropertyId}",
                        requestDto.PropertyOwnerId, createdRequest.Id, requestDto.PropertyId);
                }
                catch (Exception notificationEx)
                {
                    // Log the notification error but don't fail the entire request
                    _logger.LogError(notificationEx, "Failed to create notification for property owner {PropertyOwnerId} regarding contact request {ContactRequestId}",
                        requestDto.PropertyOwnerId, createdRequest.Id);
                    throw;
                }

                return CreatedAtAction(nameof(GetContactRequestById), new { id = createdRequest.Id }, createdRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating contact request for property {PropertyId} from IP {IpAddress}",
                    requestDto.PropertyId, GetClientIpAddress());
                return StatusCode(500, new { Message = "An error occurred while creating the contact request. Please try again later." });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateContactRequest(Guid id, [FromBody] UpdateContactRequestDto requestDto)
        {
            try
            {
                await _contactRequestService.UpdateAsync(id, requestDto);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            return NoContent();
        }

        [HttpGet("property/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<IEnumerable<ContactRequestDto>>> GetContactRequestsByPropertyId(Guid propertyId)
        {
            var requests = await _contactRequestService.GetByPropertyIdAsync(propertyId);
            
            return Ok(requests);
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> DeleteContactRequest(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting contact request {ContactRequestId} by user {UserId}", id, GetUserId());
                LogUserAction(_logger, "DeleteContactRequest", new { ContactRequestId = id });

                await _contactRequestService.DeleteAsync(id);

                _logger.LogInformation("Successfully deleted contact request {ContactRequestId}", id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Contact request {ContactRequestId} not found for deletion: {Message}", id, ex.Message);
                return NotFound(new { Message = "Contact request not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting contact request {ContactRequestId}", id);
                return StatusCode(500, new { Message = "An error occurred while deleting the contact request. Please try again later." });
            }
        }

    }
}
