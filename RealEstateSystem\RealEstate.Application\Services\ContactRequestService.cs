﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Common;

namespace RealEstate.Application.Services
{
    public class ContactRequestService : IContactRequestService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public ContactRequestService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<ContactRequestDto> GetByIdAsync(Guid id)
        {
            var request = await _unitOfWork.ContactRequests.GetByIdAsync(id);
            return _mapper.Map<ContactRequestDto>(request);
        }

        public async Task<IEnumerable<ContactRequestDto>> GetAllAsync()
        {
            var requests = await _unitOfWork.ContactRequests.GetAllAsync();
            return _mapper.Map<IEnumerable<ContactRequestDto>>(requests);
        }

        public async Task<PagedResult<ContactRequestDto>> GetPagedAsync(PagingRequest paging, string? phone)
        {
            var requests = await _unitOfWork.ContactRequests.GetPagedAsync(
                paging,
                r => string.IsNullOrEmpty(phone) || (r.Phone != null && r.Phone.Contains(phone))
            );

            return new PagedResult<ContactRequestDto>
            {
                Items = _mapper.Map<IEnumerable<ContactRequestDto>>(requests.Items),
                TotalCount = requests.TotalCount
            };
        }

        public async Task<IEnumerable<ContactRequestDto>> GetByPropertyIdAsync(Guid propertyId)
        {
            var requests = await _unitOfWork.ContactRequests.FindAsync(r => r.PropertyId == propertyId);
            return _mapper.Map<IEnumerable<ContactRequestDto>>(requests);
        }

        public async Task<IEnumerable<ContactRequestDto>> CountActionRequestByPropertyIdAsync(Guid propertyId)
        {
            var requests = await _unitOfWork.ContactRequests.FindAsync(r => r.PropertyId == propertyId && r.Status == EnumValues.ContactRequestStatus.Pending.ToString());

            return _mapper.Map<IEnumerable<ContactRequestDto>>(requests);
        }


        public async Task<ContactRequestDto> CreateAsync(CreateContactRequestDto requestDto)
        {
            var request = _mapper.Map<ContactRequest>(requestDto);

            request.SentAt = DateTime.UtcNow;
            request.Status = EnumValues.ContactRequestStatus.Pending.ToString();

            await _unitOfWork.ContactRequests.AddAsync(request);
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<ContactRequestDto>(request);
        }

        public async Task<bool> UpdateAsync(Guid id, UpdateContactRequestDto requestDto)
        {
            var request = await _unitOfWork.ContactRequests.GetByIdAsync(id);
            if (request == null) return false;

            _mapper.Map(requestDto, request);
            request.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.ContactRequests.Update(request);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var request = await _unitOfWork.ContactRequests.GetByIdAsync(id);
            if (request == null) return false;

            request.IsDeleted = true;
            request.DeletedAt = DateTime.UtcNow;

            _unitOfWork.ContactRequests.Update(request);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }
    }

}
