﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System.Security.Claims;

namespace RealEstate.API.Controllers
{
    /// <summary>
    /// Controller for authentication and user-related operations.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;
        private readonly ITokenService _tokenService;
        private readonly IUserService _userService;
        private readonly IUserAvatarService _userAvatarService;
        private readonly IYezDataService _yezDataService;

        public AuthController(IAuthService authService, ILogger<AuthController> logger, ITokenService tokenService, IUserService userService, IUserAvatarService userAvatarService, IYezDataService yezDataService)
        {
            _authService = authService;
            _logger = logger;
            _tokenService = tokenService;
            _userService = userService;
            _userAvatarService = userAvatarService;
            _yezDataService = yezDataService;
        }

        /// <summary>
        /// Retrieves the profile of the authenticated user.
        /// </summary>
        /// <returns>A ProfileDto containing user details, member rank information, and highlight fee.</returns>
        [Authorize]
        [HttpGet("me")]
        public async Task<ActionResult<ProfileDto>> GetUserProfile()
        {
            _logger.LogInformation("Attempting to retrieve user profile.");
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (userId == null)
                {
                    return Unauthorized(new { Message = "User ID not found in token" });
                }

                var parsedUserId = Guid.Parse(userId);
                var userProfile = await _userService.GetUserByIdAsync(parsedUserId);

                // Get user avatar information
                var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(parsedUserId);

                // Add the avatar URL to the user profile
                if (userAvatar != null)
                {
                    userProfile.AvatarURL = userAvatar.MediaURL;
                }

                // Get member rank details and highlight fee based on user's rank
                var memberRankDetails = await _yezDataService.GetMemberRankingByNameAsync(userProfile.MemberRank);
                var highlightFee = await _yezDataService.GetHighlightFeeByRankAsync(userProfile.MemberRank);

                // Create a response object with user profile and additional information
                var response = new ProfileDto
                {
                    User = userProfile,
                    MemberRankDetails = memberRankDetails,
                    HighlightFee = highlightFee
                };

                return Ok(response);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access attempt for user profile.");
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when retrieving user profile");
                return Unauthorized(new { Message = "An error occurred while processing your request. Please try again later." });
            }
        }

        /// <summary>
        /// Registers a new user.
        /// </summary>
        /// <param name="registerDto">The registration data.</param>
        /// <returns>A UserDto upon successful registration.</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> Register(CreateUserDto registerDto)
        {
            _logger.LogInformation("Attempting to register new user with email: {Email}", registerDto.Email);
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var user = await _authService.RegisterAsync(registerDto);
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when registering");
                return BadRequest(new { Message = "An error occurred during registration. Please try again later." });
            }
        }

        /// <summary>
        /// Authenticates a user and provides a JWT token along with a refresh token.
        /// </summary>
        /// <param name="loginDto">The login credentials.</param>
        /// <returns>A UserDto containing user details and a JWT token.</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> Login(LoginDto loginDto)
        {
            _logger.LogInformation("Attempting to log in user with email: {Email}", loginDto.Email);
            try
            {
                var user = await _authService.LoginAsync(loginDto);
                var refreshToken = _tokenService.GenerateRefreshToken();

                // Store Refresh Token in HttpOnly Secure Cookies
                Response.Cookies.Append("refreshToken", refreshToken, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true, // Enable on HTTPS
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTime.UtcNow.AddDays(7)
                });

                return Ok(user);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when logging in");
                return Unauthorized(new { Message = "An error occurred during login. Please try again later." });
            }
        }

        /// <summary>
        /// Initiates the password reset process.
        /// </summary>
        /// <param name="resetPasswordDto">The password reset data.</param>
        /// <returns>A message indicating the status of the password reset.</returns>
        [HttpPost("reset-password")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> ResetPassword(ForgotPasswordDto resetPasswordDto)
        {
            _logger.LogInformation("Attempting to reset password for email: {Email}", resetPasswordDto.Email);
            try
            {
                return Ok(new { Message = "TODO: waiting to implement sending email" });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when logging in");
                return Unauthorized(new { Message = "An error occurred during password reset. Please try again later." });
            }
        }

        /// <summary>
        /// Changes the password of the authenticated user.
        /// </summary>
        /// <param name="changePasswordDto">The data for changing the password.</param>
        /// <returns>A UserDto upon successful password change.</returns>
        [Authorize]
        [HttpPatch("/api/[controller]/me/password")]
        public async Task<ActionResult<UserDto>> Password(ChangePasswordDto changePasswordDto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _logger.LogInformation("Attempting to change password for user ID: {UserId}", userId);
            try
            {
                var user = await _authService.ChangePassword(changePasswordDto);
                user.Token = string.Empty;
                return Ok(user);
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new { Message = ex.Message, ErrorType = "invalid_credentials" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when changing password");
                return StatusCode(500, new { Message = "An error occurred while processing your request. Please try again later." });
            }
        }

        /// <summary>
        /// Refreshes the authentication token using a refresh token stored in cookies.
        /// </summary>
        /// <returns>A UserDto with a new JWT token.</returns>
        [HttpPost("refresh-token")]
        public IActionResult RefreshToken()
        {
            _logger.LogInformation("Attempting to refresh token.");
            var refreshToken = Request.Cookies["refreshToken"];
            if (string.IsNullOrEmpty(refreshToken))
                return Unauthorized(new { message = "No refresh token found" });

            // Validate token (typically refresh tokens would be stored in DB for validation)

            // Get the current user's claims
            var claimsIdentity = User.Identity as ClaimsIdentity;
            if (claimsIdentity == null)
            {
                return Unauthorized();
            }

            var userId = claimsIdentity.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId == null)
            {
                return Unauthorized();
            }

            var user = _authService.RefreshToken(Guid.Parse(userId));
            _logger.LogInformation("Refresh token successful for user ID: {UserId}", userId);
            return Ok(user);
        }

        /// <summary>
        /// Validates the current authentication token.
        /// </summary>
        /// <returns>A message indicating whether the token is valid.</returns>
        [HttpGet("validate-token")]
        [Authorize]
        public async Task<IActionResult> ValidateToken()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _logger.LogInformation("Validating token for user ID: {UserId}", userId);
            // TODO: need to implement check the token is retrieve or not
            return Ok(new { Message = "token valid" });
        }
    }
}
