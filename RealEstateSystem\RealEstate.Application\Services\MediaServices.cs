﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class MediaServices : IMediaServices
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public MediaServices(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<PropertyMediaDto>> GetAllMediaAsync(List<Guid> mediaIds)
        {
            var medias = await _unitOfWork.PropertyMedias.FindAsync(x => mediaIds.Contains(x.Id) && x.PropertyID == null);
            return _mapper.Map<IEnumerable<PropertyMediaDto>>(medias);
        }

        public async Task<PropertyMediaDto> GetMediaByIdAsync(Guid id)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id, true, null);
            return _mapper.Map<PropertyMediaDto>(media);
        }

        public async Task<IEnumerable<PropertyMediaDto>> GetAllMediaAsync()
        {
            var medias = await _unitOfWork.PropertyMedias.GetAllAsync();
            return _mapper.Map<IEnumerable<PropertyMediaDto>>(medias);
        }

        public async Task<IEnumerable<PropertyMediaDto>> GetMediaByPropertyIdAsync(Guid propertyId)
        {
            var medias = await _unitOfWork.PropertyMedias.FindAsync(m => m.PropertyID == propertyId);
            return _mapper.Map<IEnumerable<PropertyMediaDto>>(medias);
        }

        public async Task<PropertyMediaDto> CreateMediaAsync(CreateMediaDto mediaDto)
        {
            var media = _mapper.Map<PropertyMedia>(mediaDto);

            await _unitOfWork.PropertyMedias.AddAsync(media);
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<PropertyMediaDto>(media);
        }

        public async Task<PropertyMediaDto> UpdateMediaAsync(Guid id, CreateMediaDto mediaDto)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null) throw new FileNotFoundException();

            _mapper.Map(mediaDto, media);

            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<PropertyMediaDto>(media);
        }

        public async Task<bool> DeleteMediaAsync(Guid id)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null) return false;

            _unitOfWork.PropertyMedias.Remove(media);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<List<Guid>> UpdateMediaAsync(Guid propertyId, List<Guid> mediaIds)
        {
            throw new NotImplementedException();
        }

        public async Task<Guid> UpdateMediaAsync(Guid mediaId, Guid propertyId, string filePath, PropertyMediaDto propertyMedia)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(mediaId);
            if (media == null) return Guid.Empty; // Or throw an exception

            media.FilePath = filePath;
            media.PropertyID = propertyId;
            media.Caption = propertyMedia.Caption;
            media.IsAvatar = propertyMedia.IsAvatar;

            _unitOfWork.PropertyMedias.Update(media);

            await _unitOfWork.SaveChangesAsync();
            return mediaId;
        }
    }
}