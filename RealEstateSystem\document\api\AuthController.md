# AuthController API Endpoints

This document outlines the API endpoints provided by the `AuthController` for authentication and user-related operations.

---

## 1. Get User Profile

### Endpoint
`GET /api/Auth/me`

### Description
Retrieves the profile of the authenticated user.

### Authorization
Requires authentication (`[Authorize]`).

### Parameters
None

### Responses
- **200 OK**: Returns a `ProfileDto` containing user details, member rank information, and highlight fee.
- **401 Unauthorized**: If the user ID is not found in the token or access is unauthorized.
- **500 Internal Server Error**: If an error occurs while processing the request.

---

## 2. Register New User

### Endpoint
`POST /api/Auth/register`

### Description
Registers a new user.

### Authorization
No authorization required (`[AllowAnonymous]`).

### Parameters
- `registerDto` (Body): The registration data.
  - Type: `CreateUserDto`

### Responses
- **200 OK**: Returns a `UserDto` upon successful registration.
- **400 Bad Request**: If the model state is invalid or an error occurs during registration.

---

## 3. Login User

### Endpoint
`POST /api/Auth/login`

### Description
Authenticates a user and provides a JWT token along with a refresh token.

### Authorization
No authorization required (`[AllowAnonymous]`).

### Parameters
- `loginDto` (Body): The login credentials.
  - Type: `LoginDto`

### Responses
- **200 OK**: Returns a `UserDto` containing user details and a JWT token. The refresh token is stored in an HttpOnly, Secure, SameSite=Strict cookie.
- **401 Unauthorized**: If authentication fails or an error occurs during login.

---

## 4. Reset Password

### Endpoint
`POST /api/Auth/reset-password`

### Description
Initiates the password reset process.

### Authorization
No authorization required (`[AllowAnonymous]`).

### Parameters
- `resetPasswordDto` (Body): The password reset data.
  - Type: `ForgotPasswordDto`

### Responses
- **200 OK**: Returns a message indicating the status of the password reset (currently a TODO for email implementation).
- **401 Unauthorized**: If access is unauthorized.
- **500 Internal Server Error**: If an error occurs during password reset.

---

## 5. Change Password

### Endpoint
`PATCH /api/Auth/me/password`

### Description
Changes the password of the authenticated user.

### Authorization
Requires authentication (`[Authorize]`).

### Parameters
- `changePasswordDto` (Body): The data for changing the password.
  - Type: `ChangePasswordDto`

### Responses
- **200 OK**: Returns a `UserDto` upon successful password change. The `Token` field in the returned `UserDto` will be empty for security reasons.
- **400 Bad Request**: If the provided credentials are invalid.
- **500 Internal Server Error**: If an error occurs while processing the request.

---

## 6. Refresh Token

### Endpoint
`POST /api/Auth/refresh-token`

### Description
Refreshes the authentication token using a refresh token stored in cookies.

### Authorization
No explicit authorization attribute; relies on the presence and validity of the refresh token in cookies.

### Parameters
None (refresh token is read from cookies)

### Responses
- **200 OK**: Returns a `UserDto` with a new JWT token.
- **401 Unauthorized**: If no refresh token is found or validation fails.

---

## 7. Validate Token

### Endpoint
`GET /api/Auth/validate-token`

### Description
Validates the current authentication token.

### Authorization
Requires authentication (`[Authorize]`).

### Parameters
None

### Responses
- **200 OK**: Returns a message indicating whether the token is valid.
- **401 Unauthorized**: If the token is invalid or missing. 