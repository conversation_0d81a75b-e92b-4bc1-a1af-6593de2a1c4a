using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class Invoice : BaseEntity
    {
        [Required]
        public Guid UserId { get; set; }
        
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Type { get; set; } // property spending type (new_post, renew, highlight)
        
        [Required]
        public int TotalAmount { get; set; }
        
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "PENDING"; // COMPLETED, PENDING, FAILED, CANCELLED
        
        public string? Note { get; set; }
        
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? PaidAt { get; set; }
        
        // Navigation properties
        [ForeignKey("UserId")]
        public AppUser? User { get; set; }
        
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }
        
        public ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
    }
}
