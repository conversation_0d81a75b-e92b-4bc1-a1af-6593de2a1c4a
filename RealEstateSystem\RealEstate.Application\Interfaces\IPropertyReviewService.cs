﻿using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IPropertyReviewService
    {
        Task<IEnumerable<PropertyReviewDto>> GetAllReviewsAsync();
        Task<PropertyReviewDto> GetReviewByIdAsync(Guid id);
        Task<IEnumerable<PropertyReviewDto>> GetReviewsByPropertyIdAsync(Guid propertyId);
        Task<IEnumerable<PropertyReviewDto>> GetReviewsByBuyerIdAsync(Guid propertyId);        
        Task<PropertyReviewDto> CreateReviewAsync(CreatePropertyReviewDto reviewDto, Guid userId);
        Task<bool> UpdateReviewAsync(Guid id, CreatePropertyReviewDto reviewDto, Guid userId);
        Task<bool> DeleteReviewAsync(Guid id, Guid userId);
    }
}
