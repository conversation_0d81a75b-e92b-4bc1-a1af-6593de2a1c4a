﻿namespace RealEstate.Application.DTO
{
    public class PropertyFilterCriteriaDto
    {
        public List<string>? PostTypes { get; set; }
        public List<string>? PropertyTypes { get; set; }
        public string? CityId { get; set; }
        public string? DistrictId { get; set; }
        public string? Address { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public decimal? MinArea { get; set; }
        public decimal? MaxArea { get; set; }
        public int? MinRooms { get; set; }
        public int? MinToilets { get; set; }
        public string? Direction { get; set; }
        public string? Legality { get; set; }
        public decimal? MinRoadWidth { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;

        // Tọa độ góc Tây Nam (SouthWest)
        public decimal? SwLat { get; set; }
        public decimal? SwLng { get; set; }

        // Tọa độ góc <PERSON> (NorthEast)
        public decimal? NeLat { get; set; }
        public decimal? NeLng { get; set; }

    }
}