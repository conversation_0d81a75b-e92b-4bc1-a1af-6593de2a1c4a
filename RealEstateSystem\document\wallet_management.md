Wallet Management APIs
1. Get Wallet Balance
Endpoint: GET /api/Wallet/balance
Description: Retrieves the user's wallet balance and total spent amount.
Response:
{
  "balance": 1250.50,
  "totalSpent": 3450.75
}

2. Top Up Wallet
Endpoint: POST /api/Wallet/topup
Description: Adds funds to the user's wallet.
Request:
{
  "amount": 500.00,
  "paymentMethod": "Credit Card",
  "transactionReference": "CC-PAYMENT-123456"
}
Response:
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "amount": 500.00,
  "type": "deposit",
  "description": "Top-up via Credit Card. Ref: CC-PAYMENT-123456",
  "createdAt": "2023-09-15T10:30:45.123Z"
}

3. Spend from Wallet
Endpoint: POST /api/Wallet/spend
Description: Spends funds from the user's wallet.
Request:
{
  "amount": 50.00,
  "description": "Premium listing for property ID: PROP12345"
}

Response:
{
  "id": "4fa85f64-5717-4562-b3fc-2c963f66afa7",
  "amount": 50.00,
  "type": "spend",
  "description": "Premium listing for property ID: PROP12345",
  "createdAt": "2023-09-15T11:20:30.456Z"
}

4. Get Wallet Transactions
Endpoint: GET /api/Wallet/transactions?page=1&pageSize=5
Description: Gets the transaction history for the user's wallet.
Query Parameters:
page (optional): Page number (default: 1)
pageSize (optional): Number of transactions per page (default: 20)
Example URL: /api/Wallet/transactions?page=1&pageSize=5
Response:
[
  {
    "id": "4fa85f64-5717-4562-b3fc-2c963f66afa7",
    "amount": 50.00,
    "type": "spend",
    "description": "Premium listing for property ID: PROP12345",
    "createdAt": "2023-09-15T11:20:30.456Z"
  },
  {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "amount": 500.00,
    "type": "deposit",
    "description": "Top-up via Credit Card. Ref: CC-PAYMENT-123456",
    "createdAt": "2023-09-15T10:30:45.123Z"
  },
  {
    "id": "5fa85f64-5717-4562-b3fc-2c963f66afa8",
    "amount": 100.00,
    "type": "spend",
    "description": "Highlight property feature for ID: PROP67890",
    "createdAt": "2023-09-14T09:15:10.789Z"
  },
  {
    "id": "6fa85f64-5717-4562-b3fc-2c963f66afa9",
    "amount": 1000.00,
    "type": "deposit",
    "description": "Top-up via Bank Transfer. Ref: BT-987654",
    "createdAt": "2023-09-10T14:45:20.123Z"
  },
  {
    "id": "7fa85f64-5717-4562-b3fc-2c963f66afaa",
    "amount": 75.00,
    "type": "spend",
    "description": "Premium listing for property ID: PROP54321",
    "createdAt": "2023-09-05T16:30:45.789Z"
  }
]

Wallet Implementation
The wallet system:
Automatically creates a wallet for a user if one doesn't exist
Tracks all deposits and spending
Updates user's total spent amount when spending
Automatically updates user ranking based on spending thresholds
Prevents spending more than the available balance
Security