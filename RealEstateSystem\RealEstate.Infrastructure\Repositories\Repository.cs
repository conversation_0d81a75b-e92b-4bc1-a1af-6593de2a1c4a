﻿using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System.Linq.Expressions;

namespace RealEstate.Infrastructure.Repositories
{
    public class Repository<T> : IRepository<T> where T : class, IEntity
    {
        protected readonly ApplicationDbContext _context;
        protected readonly DbSet<T> _dbSet;

        public Repository(ApplicationDbContext context)
        {
            _context = context;
            _dbSet = context.Set<T>();
        }

        public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, bool asNoTracking = true, params Expression<Func<T, object>>[]? includes)
        {
            var query = _dbSet.AsQueryable();

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            if (includes != null)
            {
                query = includes.Aggregate(query, (current, include) => current.Include(include));
            }

            return await query.Where(predicate).ToListAsync();
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync(bool asNoTracking = true, params Expression<Func<T, object>>[]? includes)
        {
            var query = _dbSet.AsQueryable();

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            if (includes != null)
            {
                query = includes.Aggregate(query, (current, include) => current.Include(include));
            }

            return await query.ToListAsync();
        }

        public virtual async Task<T> GetByIdAsync(Guid id, bool asNoTracking = true, params Expression<Func<T, object>>[]? includes)
        {
            var query = _dbSet.AsQueryable();

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            if (includes != null)
            {
                query = includes.Aggregate(query, (current, include) => current.Include(include));
            }

            return await query.FirstOrDefaultAsync(e => e.Id == id);
        }

        public virtual async Task<PagedResult<T>> GetPagedAsync(PagingRequest paging, Expression<Func<T, bool>>? predicate = null, bool asNoTracking = true, params Expression<Func<T, object>>[] includes)
        {
            var query = _dbSet.AsQueryable();

            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            if (includes != null && includes.Count() > 0)
            {
                query = includes.Aggregate(query, (current, include) => current.Include(include));
            }

            if (predicate != null)
            {
                query = query.Where(predicate);
            }

            // Get total count before applying pagination
            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(paging.SortColumn))
            {
                var parameter = Expression.Parameter(typeof(T), "x");
                var property = Expression.Property(parameter, paging.SortColumn);
                var lambda = Expression.Lambda(property, parameter);

                var methodName = paging.SortDescending ? "OrderByDescending" : "OrderBy";
                var orderByExpression = Expression.Call(typeof(Queryable), methodName,
                    new Type[] { typeof(T), property.Type },
                    query.Expression, Expression.Quote(lambda));

                query = query.Provider.CreateQuery<T>(orderByExpression);
            }

            // Apply pagination
            var items = await query
                .Skip((paging.PageNumber - 1) * paging.PageSize)
                .Take(paging.PageSize)
                .ToListAsync();

            return new PagedResult<T>
            {
                Items = items,
                TotalCount = totalCount
            };
        }

        public virtual async Task AddAsync(T entity)
        {
            await _dbSet.AddAsync(entity);
        }

        public virtual void Update(T entity)
        {
            _dbSet.Update(entity);
        }

        public void Remove(T entity)
        {
            if (entity is BaseEntityWithAuditable auditableEntity)
            {
                auditableEntity.IsDeleted = true;
                auditableEntity.DeletedAt = DateTime.UtcNow;
                Update(entity);
            }
            else
            {
                _dbSet.Remove(entity);
            }
        }

        public void RestoreAsync(T entity)
        {
            if (entity is BaseEntityWithAuditable auditableEntity)
            {
                auditableEntity.IsDeleted = false;
                Update(entity);
            }
            else
            {
                // do nothing
            }
        }

        public IQueryable<T> GetQueryable()
        {
            return _context.Set<T>().AsQueryable();
        }
    }
}
