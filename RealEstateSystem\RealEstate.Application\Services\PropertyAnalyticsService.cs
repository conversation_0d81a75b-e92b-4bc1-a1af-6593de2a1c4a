using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using RealEstate.Domain.Common;
using System.Text.Json;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.Services
{
    public class PropertyAnalyticsService : IPropertyAnalyticsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public PropertyAnalyticsService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task LogPropertyViewAsync(LogPropertyViewDto dto)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(dto.PropertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {dto.PropertyId} not found.");
            }

            var viewLog = new PropertyEngagementView
            {
                PropertyId = dto.PropertyId,
                ViewerId = dto.ViewerId,
                ViewerIP = dto.ViewerIp,
                ViewedAt = DateTime.UtcNow,
                UserAgent = dto.UserAgent,
                ReferrerUrl = dto.ReferrerUrl,
                SessionId = dto.SessionId,
                DeviceId = dto.DeviceId,
                DeviceType = dto.DeviceType,
                Platform = dto.Platform,
                Browser = dto.Browser,
                City = dto.City,
                Region = dto.Region,
                Country = dto.Country
            };

            await _unitOfWork.PropertyEngagementViews.AddAsync(viewLog);
            await _unitOfWork.SaveChangesAsync();
        }



        public async Task LogPropertyEngagementEventAsync(LogPropertyEngagementEventDto dto)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(dto.PropertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {dto.PropertyId} not found.");
            }

            var engagementEvent = new PropertyEngagementEvent
            {
                PropertyId = dto.PropertyId,
                UserId = dto.UserId,
                EventType = dto.EventType,
                SessionId = dto.SessionId,
                DeviceId = dto.DeviceId,
                UserAgent = dto.UserAgent,
                IpAddress = dto.IpAddress,
                DeviceType = dto.DeviceType,
                Platform = dto.Platform,
                Browser = dto.Browser,
                City = dto.City,
                Region = dto.Region,
                Country = dto.Country,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.PropertyEngagementEvents.AddAsync(engagementEvent);
            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<PropertyAnalyticsDto> GetPropertyAnalyticsAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");
            }

            // Set default date range if not provided
            startDate ??= DateTime.UtcNow.AddMonths(-1);
            endDate ??= DateTime.UtcNow;

            // Get property views within the date range
            var viewLogs = await _unitOfWork.PropertyEngagementViews.GetQueryable()
                .Where(v => v.PropertyId == propertyId && v.ViewedAt >= startDate && v.ViewedAt <= endDate)
                .ToListAsync();



            // Get favorites count
            var favoritesCount = await _unitOfWork.UserFavorites.GetQueryable()
                .CountAsync(f => f.PropertyID == propertyId);

            // Get or create engagement summary
            var engagementSummary = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                .FirstOrDefaultAsync(s => s.PropertyId == propertyId);

            // Calculate daily views
            var dailyViews = viewLogs
                .GroupBy(v => v.ViewedAt.Date)
                .Select(g => new DailyViewsDto
                {
                    Date = g.Key,
                    Views = g.Count(),
                })
                .OrderBy(d => d.Date)
                .ToList();



            // Create the analytics DTO
            var analyticsDto = new PropertyAnalyticsDto
            {
                PropertyId = propertyId,
                PropertyTitle = property.Name,
                PropertyStatus = property.Status,
                CreatedAt = property.CreatedAt,
                ExpiresAt = property.ExpiresAt,

                // Engagement metrics
                TotalViews = engagementSummary?.TotalViews ?? 0,
                TotalFavorites = favoritesCount,

                // Trend data
                ViewsTrend = dailyViews
            };

            return analyticsDto;
        }

        public async Task<PagedResultDto<PropertyAnalyticsDto>> GetUserPropertiesAnalyticsAsync(Guid userId, PropertyAnalyticsFilterDto filter)
        {
            // Get user's properties
            var propertiesQuery = _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId);

            // Apply filters
            if (filter.StartDate.HasValue)
            {
                propertiesQuery = propertiesQuery.Where(p => p.CreatedAt >= filter.StartDate.Value);
            }

            if (filter.EndDate.HasValue)
            {
                propertiesQuery = propertiesQuery.Where(p => p.CreatedAt <= filter.EndDate.Value);
            }

            if (filter.PropertyStatuses != null && filter.PropertyStatuses.Any())
            {
                propertiesQuery = propertiesQuery.Where(p => filter.PropertyStatuses.Contains(p.Status));
            }

            // Get total count before pagination
            var totalCount = await propertiesQuery.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(filter.SortBy))
            {
                switch (filter.SortBy.ToLower())
                {
                    case "createdat":
                        propertiesQuery = filter.SortDescending
                            ? propertiesQuery.OrderByDescending(p => p.CreatedAt)
                            : propertiesQuery.OrderBy(p => p.CreatedAt);
                        break;
                    case "expiresat":
                        propertiesQuery = filter.SortDescending
                            ? propertiesQuery.OrderByDescending(p => p.ExpiresAt)
                            : propertiesQuery.OrderBy(p => p.ExpiresAt);
                        break;
                    case "name":
                        propertiesQuery = filter.SortDescending
                            ? propertiesQuery.OrderByDescending(p => p.Name)
                            : propertiesQuery.OrderBy(p => p.Name);
                        break;
                    default:
                        propertiesQuery = propertiesQuery.OrderByDescending(p => p.CreatedAt);
                        break;
                }
            }
            else
            {
                propertiesQuery = propertiesQuery.OrderByDescending(p => p.CreatedAt);
            }

            // Apply pagination
            var properties = await propertiesQuery
                .OrderByDescending(n => n.CreatedAt)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Get property IDs for further queries
            var propertyIds = properties.Select(p => p.Id).ToList();

            // Get engagement summaries for these properties
            var engagementSummaries = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                .Where(s => propertyIds.Contains(s.PropertyId))
                .ToListAsync();

            // Get favorites counts
            var favoritesCounts = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => propertyIds.Contains(f.PropertyID))
                .GroupBy(f => f.PropertyID)
                .Select(g => new { PropertyId = g.Key, Count = g.Count() })
                .ToListAsync();

            // Create analytics DTOs
            var analyticsDtos = new List<PropertyAnalyticsDto>();

            foreach (var property in properties)
            {
                var summary = engagementSummaries.FirstOrDefault(s => s.PropertyId == property.Id);
                var favoritesCount = favoritesCounts.FirstOrDefault(f => f.PropertyId == property.Id)?.Count ?? 0;

                // Apply additional filters
                if (filter.MinViews.HasValue && (summary?.TotalViews ?? 0) < filter.MinViews.Value)
                    continue;

                if (filter.MaxViews.HasValue && (summary?.TotalViews ?? 0) > filter.MaxViews.Value)
                    continue;

                if (filter.MinSpent.HasValue && (summary?.TotalSpent ?? 0) < filter.MinSpent.Value)
                    continue;

                if (filter.MaxSpent.HasValue && (summary?.TotalSpent ?? 0) > filter.MaxSpent.Value)
                    continue;

                var analyticsDto = new PropertyAnalyticsDto
                {
                    PropertyId = property.Id,
                    PropertyTitle = property.Name,
                    PropertyStatus = property.Status,
                    CreatedAt = property.CreatedAt,
                    ExpiresAt = property.ExpiresAt,
                    
                    // Engagement metrics
                    TotalViews = summary?.TotalViews ?? 0,
                    TotalFavorites = favoritesCount,
                    
                    // Financial metrics
                    TotalSpent = summary?.TotalSpent ?? 0,
                    ExtensionSpent = summary?.ExtensionSpent ?? 0,
                    HighlightSpent = summary?.HighlightSpent ?? 0,
                    
                    // Trend data will be empty for list view
                    ViewsTrend = new List<DailyViewsDto>(),
                    SpendingTrend = new List<DailySpendingDto>()
                };

                analyticsDtos.Add(analyticsDto);
            }

            // Calculate page count
            var pageCount = (int)Math.Ceiling(totalCount / (double)filter.PageSize);

            // Create paged result
            var pagedResult = new PagedResultDto<PropertyAnalyticsDto>
            {
                Items = analyticsDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = filter.Page,
                PageSize = filter.PageSize
            };

            return pagedResult;
        }

        public async Task<byte[]> ExportPropertyAnalyticsToExcelAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var analytics = await GetPropertyAnalyticsAsync(propertyId, startDate, endDate);
            
            // TODO: Implement Excel export functionality
            // This would typically use a library like EPPlus or ClosedXML to create an Excel file
            
            // For now, return a placeholder
            var jsonBytes = JsonSerializer.SerializeToUtf8Bytes(analytics);
            return jsonBytes;
        }

        public async Task<byte[]> ExportUserPropertiesAnalyticsToExcelAsync(Guid userId, PropertyAnalyticsFilterDto filter)
        {
            var analytics = await GetUserPropertiesAnalyticsAsync(userId, filter);
            
            // TODO: Implement Excel export functionality
            // This would typically use a library like EPPlus or ClosedXML to create an Excel file
            
            // For now, return a placeholder
            var jsonBytes = JsonSerializer.SerializeToUtf8Bytes(analytics);
            return jsonBytes;
        }

        public async Task UpdatePropertyEngagementSummaryAsync(Guid propertyId)
        {
            // Get property to ensure it exists
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");
            }

            await UpdatePropertyEngagementSummaryAsync(property);
        }

        // Overload that accepts Property object to avoid duplicate DB calls
        public async Task<PropertyEngagementSummary> UpdatePropertyEngagementSummaryAsync(Property property)
        {
            if (property == null)
            {
                throw new ArgumentNullException(nameof(property));
            }

            var propertyId = property.Id;

            // Get existing summary or create new one
            var summary = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                .FirstOrDefaultAsync(s => s.PropertyId == propertyId);

            var isNew = false;
            if (summary == null)
            {
                summary = new PropertyEngagementSummary
                {
                    PropertyId = propertyId,
                    LastUpdatedAt = DateTime.UtcNow
                };
                isNew = true;
            }

            // Calculate total views
            var totalViews = await _unitOfWork.PropertyEngagementViews.GetQueryable()
                .CountAsync(v => v.PropertyId == propertyId);



            // Calculate new engagement metrics from PropertyEngagementEvent
            var engagementEvents = await _unitOfWork.PropertyEngagementEvents.GetQueryable()
                .Where(e => e.PropertyId == propertyId)
                .ToListAsync();

            var totalClicksPhone = engagementEvents
                .Count(e => e.EventType == FunctionHelper.GetEnumDescription(PropertyEngagementEventType.ClickPhone));
            var totalClicksChat = engagementEvents
                .Count(e => e.EventType == FunctionHelper.GetEnumDescription(PropertyEngagementEventType.Chat));
            var totalSearchImpressions = engagementEvents
                .Count(e => e.EventType == FunctionHelper.GetEnumDescription(PropertyEngagementEventType.SearchImpression));
            var totalClickThroughs = engagementEvents
                .Count(e => e.EventType == FunctionHelper.GetEnumDescription(PropertyEngagementEventType.ClickThrough));
            var conversionCount = engagementEvents
                .Count(e => e.EventType == FunctionHelper.GetEnumDescription(PropertyEngagementEventType.Conversion));
            var totalFavorites = engagementEvents
                .Count(e => e.EventType == FunctionHelper.GetEnumDescription(PropertyEngagementEventType.Favorite));

            // Get last viewed date
            var lastViewedAt = await _unitOfWork.PropertyEngagementViews.GetQueryable()
                .Where(v => v.PropertyId == propertyId)
                .OrderByDescending(v => v.ViewedAt)
                .Select(v => v.ViewedAt)
                .FirstOrDefaultAsync();

            // Calculate average view duration (placeholder - would need actual duration tracking)
            // For now, we'll use a simple estimate based on engagement events
            var averageViewDuration = engagementEvents
                .Where(e => e.EventType == FunctionHelper.GetEnumDescription(PropertyEngagementEventType.View))
                .Any() ? 60 : (int?)null; // Default 60 seconds if views exist

            // Update summary
            summary.TotalViews = totalViews;
            summary.TotalFavorites = totalFavorites;
            summary.TotalClicksPhone = totalClicksPhone;
            summary.TotalClicksChat = totalClicksChat;
            summary.TotalSearchImpressions = totalSearchImpressions;
            summary.TotalClickThroughs = totalClickThroughs;
            summary.ConversionCount = conversionCount;
            summary.LastViewedAt = lastViewedAt;
            summary.AverageViewDuration = averageViewDuration;
            summary.LastUpdatedAt = DateTime.UtcNow;

            if (isNew)
            {
                await _unitOfWork.PropertyEngagementSummaries.AddAsync(summary);
            }
            else
            {
                _unitOfWork.PropertyEngagementSummaries.Update(summary);
            }

            await _unitOfWork.SaveChangesAsync();
            return summary;
        }

        public async Task<object> GetPropertyEngagementSummaryAsync(Guid propertyId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");
            }

            // Get the engagement summary from the database
            var summary = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                .FirstOrDefaultAsync(s => s.PropertyId == propertyId);

            // If no summary exists, create one with default values
            if (summary == null)
            {
                // Update the summary and get it back directly (no need for second DB query)
                summary = await UpdatePropertyEngagementSummaryAsync(property);
            }

            // Map to the format expected by the frontend
            var result = new
            {
                views = summary?.TotalViews ?? 0,
                impressions = summary?.TotalSearchImpressions ?? 0,
                cartAdds = summary?.TotalFavorites ?? 0, // Using favorites as cart adds
                contactRequests = summary?.TotalClicksPhone + summary?.TotalClicksChat ?? 0,
                highlightsCount = summary?.TotalClickThroughs ?? 0, // Using click throughs as highlights count
                renewalsCount = summary?.ConversionCount ?? 0, // Using conversions as renewals count
                postCost = property.Price, 
                highlightCost = summary?.HighlightSpent ?? 0,
                renewalCost = summary?.ExtensionSpent ?? 0,
                totalCost = (summary?.TotalSpent ?? 0)
            };

            return result;
        }

        public async Task UpdateAllPropertiesEngagementSummaryAsync()
        {
            // Calculate the cutoff time (last 24 hours + some buffer for safety)
            var cutoffTime = DateTime.UtcNow.AddHours(-25); // 25 hours to ensure we don't miss anything

            // Get property IDs that have had activity since the last run
            var propertiesWithRecentViews = await _unitOfWork.PropertyEngagementViews.GetQueryable()
                .Where(v => v.ViewedAt >= cutoffTime)
                .Select(v => v.PropertyId)
                .Distinct()
                .ToListAsync();

            var propertiesWithRecentEvents = await _unitOfWork.PropertyEngagementEvents.GetQueryable()
                .Where(e => e.CreatedAt >= cutoffTime)
                .Select(e => e.PropertyId)
                .Distinct()
                .ToListAsync();

            // Combine all property IDs that have had recent activity
            var activePropertyIds = propertiesWithRecentViews
                .Union(propertiesWithRecentEvents)
                .Distinct()
                .ToList();

            // If no properties have recent activity, no need to update anything
            if (!activePropertyIds.Any())
            {
                return;
            }

            // Update only properties that have had recent activity
            foreach (var propertyId in activePropertyIds)
            {
                try
                {
                    await UpdatePropertyEngagementSummaryAsync(propertyId);
                }
                catch (Exception)
                {
                    // Log the error but continue with other properties
                    // In a real implementation, you would use a logger here
                }
            }
        }
    }
}
