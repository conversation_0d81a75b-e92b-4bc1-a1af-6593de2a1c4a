﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;

namespace RealEstate.InternalAPI.Controllers
{
    /// <summary>
    /// Controller for authentication and user-related operations.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Authenticates a user and provides a JWT token along with a refresh token.
        /// </summary>
        /// <param name="loginDto">The login credentials.</param>
        /// <returns>A UserDto containing user details and a JWT token.</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> Login(LoginDto loginDto)
        {
            _logger.LogInformation("Attempting to log in user with email: {Email}", loginDto.Email);
            try
            {
                var user = await _authService.LoginAsync(loginDto);
                if (user == null || user.Token == null || user.UserType != EnumValues.UserType.Admin.ToString())
                {
                    _logger.LogWarning("Unauthorized: {Email}", loginDto.Email);
                    return Unauthorized(new { Message = "Unauthorized" });
                }

                // Store Refresh Token in HttpOnly Secure Cookies
                Response.Cookies.Append("_yh.utk", user.Token, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true, // Enable on HTTPS
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTime.UtcNow.AddHours(7)
                });

                // Remove token from response to frontend (token is set in cookie)
                var userResponse = new UserAminDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FullName = user.FullName,
                    Phone = user.Phone,
                    LastLogin = user.LastLogin,
                    AvatarImage = user.AvatarImage,
                    AvatarURL = user.AvatarURL,
                    UserType = user.UserType,
                    Roles = user.Roles,
                    Permissions = user.Permissions,
                };

                return Ok(new { Message = "Login successful", Data = userResponse });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when logging in");
                return Unauthorized(new { Message = "An error occurred during login. Please try again later." });
            }
        }

        [HttpPost("logout")]
        public IActionResult Logout()
        {
            Response.Cookies.Delete("jwt");
            return Ok(new { Message = "Logout successful" });
        }

    }
}
