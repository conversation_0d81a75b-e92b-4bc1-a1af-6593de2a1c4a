using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using RealEstate.API.Attributes;
using RealEstate.API.DTO;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PropertyAnalyticsController : BaseController
    {
        private readonly IPropertyAnalyticsService _analyticsService;
        private readonly IPropertyService _propertyService;
        private readonly ILogger<PropertyAnalyticsController> _logger;
        private readonly IMemoryCache _cache;

        public PropertyAnalyticsController(
            IPropertyAnalyticsService analyticsService,
            IPropertyService propertyService,
            ILogger<PropertyAnalyticsController> logger,
            IMemoryCache cache)
        {
            _analyticsService = analyticsService;
            _propertyService = propertyService;
            _logger = logger;
            _cache = cache;
        }

        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<PropertyAnalyticsDto>> GetPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedAnalyticsAccess", $"User attempted to access analytics for property {propertyId} without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogInformation("Retrieving analytics for property {PropertyId} by user {UserId} - StartDate: {StartDate}, EndDate: {EndDate}",
                    propertyId, userId.Value, startDate, endDate);
                LogUserAction(_logger, "GetPropertyAnalytics", new { PropertyId = propertyId, StartDate = startDate, EndDate = endDate });

                var analytics = await _analyticsService.GetPropertyAnalyticsAsync(propertyId, startDate, endDate);

                _logger.LogInformation("Successfully retrieved analytics for property {PropertyId}", propertyId);
                return Ok(analytics);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property analytics for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property analytics");
            }
        }

        [HttpGet("user")]
        public async Task<ActionResult<PagedResultDto<PropertyAnalyticsDto>>> GetUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                // Create a unique cache key based on userId and filter parameters
                string filterHash = FunctionHelper.ComputeSha256Hash(Newtonsoft.Json.JsonConvert.SerializeObject(filter));
                string cacheKey = $"UserPropertiesAnalytics:{userId.Value}:{filterHash}";

                if (_cache.TryGetValue(cacheKey, out PagedResultDto<PropertyAnalyticsDto>? analytics))
                {
                    _logger.LogInformation("Successfully retrieved user properties analytics for user {UserId} from cache", userId.Value);
                    return Ok(analytics);
                }

                analytics = await _analyticsService.GetUserPropertiesAnalyticsAsync(userId.Value, filter);

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(5))
                    .SetAbsoluteExpiration(TimeSpan.FromMinutes(15));

                _cache.Set(cacheKey, analytics, cacheEntryOptions);

                _logger.LogInformation("Successfully retrieved user properties analytics for user {UserId} from database and cached them", userId.Value);
                return Ok(analytics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user properties analytics");
                return StatusCode(500, "An error occurred while retrieving user properties analytics");
            }
        }

        [HttpGet("property/{propertyId}/export")]
        public async Task<IActionResult> ExportPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var excelBytes = await _analyticsService.ExportPropertyAnalyticsToExcelAsync(propertyId, startDate, endDate);
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"property_analytics_{propertyId}.xlsx");
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting property analytics for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while exporting property analytics");
            }
        }

        [HttpGet("user/export")]
        public async Task<IActionResult> ExportUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var excelBytes = await _analyticsService.ExportUserPropertiesAnalyticsToExcelAsync(userId.Value, filter);
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "user_properties_analytics.xlsx");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting user properties analytics");
                return StatusCode(500, "An error occurred while exporting user properties analytics");
            }
        }

        [HttpPost("property/{propertyId}/view")]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyView(Guid propertyId, [FromBody] LogPropertyViewRequest request)
        {
            try
            {
                var viewerId = GetUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                var referrer = Request.Headers["Referer"].ToString();

                _logger.LogInformation("Logging property view for property {PropertyId} - ViewerId: {ViewerId}, IP: {IpAddress}",
                    propertyId, viewerId, ipAddress);

                var dto = new LogPropertyViewDto
                {
                    PropertyId = propertyId,
                    ViewerId = viewerId,
                    ViewerIp = ipAddress,
                    UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                    ReferrerUrl = referrer,
                    SessionId = request.SessionId,
                    DeviceId = request.DeviceId,
                    DeviceType = request.DeviceType,
                    Platform = request.Platform,
                    Browser = request.Browser,
                    City = null, // Geo-location temporarily set to null
                    Region = null,
                    Country = null
                };

                await _analyticsService.LogPropertyViewAsync(dto);

                _logger.LogInformation("Successfully logged property view for property {PropertyId}", propertyId);
                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property view for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while logging property view");
            }
        }

        /// <summary>
        /// Get the status history of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>List of property status history entries</returns>
        /// <response code="200">Returns the property history</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("history/status/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<IEnumerable<PropertyStatusLogDto>>> GetPropertyHistoryStatus(Guid propertyId)
        {
            try
            {
                var history = await _propertyService.GetPropertyHistoryStatus(propertyId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property history for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property history");
            }
        }

        /// <summary>
        /// Log property engagement event (matches frontend /api/log/property-event route)
        /// </summary>
        [HttpPost("/api/log/property-event")]
        [ServiceFilter(typeof(EnforceFrontendOriginAttribute))]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyEvent([FromBody] LogPropertyEventRequest request)
        {
            try
            {
                // Model validation handles event type validation via ValidPropertyEngagementEventType attribute
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (!Guid.TryParse(request.PropertyId, out var propertyId))
                {
                    return BadRequest("Invalid property ID format");
                }

                var userId = GetUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

                _logger.LogInformation("Logging property engagement event {EventType} for property {PropertyId} - UserId: {UserId}",
                    request.EventType, propertyId, userId);

                var dto = new LogPropertyEngagementEventDto
                {
                    PropertyId = propertyId,
                    UserId = userId,
                    EventType = request.EventType,
                    SessionId = request.SessionId,
                    DeviceId = request.DeviceId,
                    UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                    IpAddress = ipAddress,
                    DeviceType = request.DeviceType,
                    Platform = request.Platform,
                    Browser = request.Browser,
                    City = null, // Geo-location temporarily set to null
                    Region = null,
                    Country = null
                };

                await _analyticsService.LogPropertyEngagementEventAsync(dto);

                _logger.LogInformation("Successfully logged property engagement event {EventType} for property {PropertyId}",
                    request.EventType, propertyId);
                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property engagement event {EventType} for property {PropertyId}",
                    request.EventType, request.PropertyId);
                return StatusCode(500, "An error occurred while logging property engagement event");
            }
        }

        /// <summary>
        /// Log property view (matches frontend /api/log/property-view route)
        /// </summary>
        [HttpPost("/api/log/property-view")]
        [ServiceFilter(typeof(EnforceFrontendOriginAttribute))]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyViewFromFrontend([FromBody] LogPropertyViewRequest request)
        {
            try
            {
                if (!Guid.TryParse(request.PropertyId, out var propertyId))
                {
                    return BadRequest("Invalid property ID format");
                }

                var viewerId = GetUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                var referrer = Request.Headers["Referer"].ToString();

                _logger.LogInformation("Logging property view for property {PropertyId} - ViewerId: {ViewerId}, IP: {IpAddress}",
                    propertyId, viewerId, ipAddress);

                var dto = new LogPropertyViewDto
                {
                    PropertyId = propertyId,
                    ViewerId = viewerId,
                    ViewerIp = ipAddress,
                    UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                    ReferrerUrl = referrer,
                    SessionId = request.SessionId,
                    DeviceId = request.DeviceId,
                    DeviceType = request.DeviceType,
                    Platform = request.Platform,
                    Browser = request.Browser,
                    City = null, // Geo-location temporarily set to null
                    Region = null,
                    Country = null
                };

                await _analyticsService.LogPropertyViewAsync(dto);

                _logger.LogInformation("Successfully logged property view for property {PropertyId}", propertyId);
                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property view for property {PropertyId}", request.PropertyId);
                return StatusCode(500, "An error occurred while logging property view");
            }
        }

        /// <summary>
        /// Get property engagement summary by property ID
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>Property engagement summary data</returns>
        /// <response code="200">Returns the property engagement summary</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="404">If the property is not found</response>
        [HttpGet("property/{propertyId}/summary")]
        [Authorize]
        public async Task<ActionResult<object>> GetPropertyEngagementSummary(Guid propertyId)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedSummaryAccess", $"User attempted to access summary for property {propertyId} without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogInformation("Retrieving engagement summary for property {PropertyId} by user {UserId}", propertyId, userId.Value);
                LogUserAction(_logger, "GetPropertyEngagementSummary", new { PropertyId = propertyId });

                string cacheKey = $"PropertySummary:{propertyId}:{userId.Value}";

                if (!_cache.TryGetValue(cacheKey, out object? summary))
                {
                    summary = await _analyticsService.GetPropertyEngagementSummaryAsync(propertyId);

                    var cacheEntryOptions = new MemoryCacheEntryOptions()
                        .SetAbsoluteExpiration(TimeSpan.FromSeconds(300));

                    _cache.Set(cacheKey, summary, cacheEntryOptions);
                }

                _logger.LogInformation("Successfully retrieved engagement summary for property {PropertyId}", propertyId);
                return Ok(summary);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property engagement summary for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property engagement summary");
            }
        }


    }
}
