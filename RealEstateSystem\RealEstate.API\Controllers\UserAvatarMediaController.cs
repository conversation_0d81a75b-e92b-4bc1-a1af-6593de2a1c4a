using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("[controller]")]
    public class UserAvatarMediaController : Controller
    {
        private readonly IUserAvatarService _userAvatarService;
        private readonly ILogger<UserAvatarMediaController> _logger;

        public UserAvatarMediaController(IUserAvatarService userAvatarService, ILogger<UserAvatarMediaController> logger)
        {
            _userAvatarService = userAvatarService;
            _logger = logger;
        }

        [HttpGet("{fileId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetMedia(Guid fileId, [FromQuery] string size = null)
        {
            try
            {
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                _logger.LogInformation("Serving user avatar media {FileId} with size {Size} from IP {IpAddress}",
                    fileId, size ?? "original", ipAddress);

                var userAvatar = await _userAvatarService.GetUserAvatarByIdAsync(fileId);
                if (userAvatar == null)
                {
                    _logger.LogWarning("User avatar {FileId} not found", fileId);
                    return NotFound(new { Message = "Avatar not found" });
                }

                string filePath = userAvatar?.FilePath ?? string.Empty;

                if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("User avatar file not found on disk for avatar {FileId}, path: {FilePath}", fileId, filePath);
                    return NotFound(new { Message = "Avatar file not found" });
                }

                _logger.LogInformation("Successfully serving user avatar {FileId} from path {FilePath}", fileId, filePath);
                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                return File(fileStream, userAvatar.MediaType!);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error serving user avatar media {FileId}", fileId);
                return StatusCode(500, new { Message = "An error occurred while retrieving the avatar. Please try again later." });
            }
        }
    }
}
