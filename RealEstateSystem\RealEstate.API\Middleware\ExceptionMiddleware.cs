﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.Net;

public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionMiddleware> _logger;
    private readonly IHostEnvironment _env;

    public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger, IHostEnvironment env)
    {
        _next = next;
        _logger = logger;
        _env = env;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);

            // Check for 403 Forbidden AFTER the next middleware has run
            if (context.Response.StatusCode == 403)
            {
                await HandleAuthorizationFailureAsync(context);
            }
        }
        catch (SecurityTokenExpiredException ex)
        {
            await HandleTokenExpiredAsync(context, ex);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleAuthorizationFailureAsync(HttpContext context)
    {
        _logger.LogWarning($"403: Authorization failed {context.Response}");

        var problemDetails = new ProblemDetails
        {
            Status = StatusCodes.Status403Forbidden,
            Title = "Authorization failed",
            Detail = "Bạn không có quyền truy cập chức năng này vì một vài lý do (account bị khóa hoặc account không tồn tại). Vui lòng liên hệ Admin để được hỗ trợ."
        };

        await WriteProblemDetailsAsync(context, problemDetails);
    }

    private async Task HandleTokenExpiredAsync(HttpContext context, SecurityTokenExpiredException ex)
    {
        _logger.LogWarning($"Token expired: {ex.Message}");

        var problemDetails = new ProblemDetails
        {
            Status = StatusCodes.Status401Unauthorized,
            Title = "token_expired",
            Detail = "Your session has expired. Please log in again."
        };

        await WriteProblemDetailsAsync(context, problemDetails);
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception ex)
    {
        _logger.LogError(ex, $"An unhandled exception occurred: {ex.Message}");

        var problemDetails = new ProblemDetails
        {
            Status = StatusCodes.Status500InternalServerError,
            Title = "Internal Server Error"
        };

        if (_env.IsDevelopment())
        {
            problemDetails.Detail = ex.StackTrace;
        }

        await WriteProblemDetailsAsync(context, problemDetails);
    }

    private async Task WriteProblemDetailsAsync(HttpContext context, ProblemDetails problemDetails)
    {
        context.Response.ContentType = "application/problem+json";
        context.Response.StatusCode = problemDetails.Status ?? StatusCodes.Status500InternalServerError;
        await context.Response.WriteAsJsonAsync(problemDetails);
        
    }
}