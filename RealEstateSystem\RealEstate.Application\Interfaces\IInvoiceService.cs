using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IInvoiceService
    {
        // Core CRUD Operations
        Task<InvoiceDto> GetInvoiceByIdAsync(Guid id);
        Task<InvoiceDto> CreateInvoiceAsync(CreateInvoiceDto dto, Guid userId);
        Task<InvoiceDto> UpdateInvoiceAsync(Guid id, UpdateInvoiceDto dto, Guid userId);
        Task<bool> DeleteInvoiceAsync(Guid id, Guid userId);

        // User-Based Retrieval
        Task<PagedResultDto<InvoiceDto>> GetInvoicesByUserAsync(Guid userId, InvoiceFilterDto filter);
        Task<IEnumerable<InvoiceDto>> GetUserInvoicesByStatusAsync(Guid userId, string status);
        Task<IEnumerable<InvoiceDto>> GetUserInvoicesByTypeAsync(Guid userId, string type);
        Task<InvoiceStatsDto> GetUserInvoiceStatsAsync(Guid userId);
        Task<InvoiceStatsDto> GetUserInvoiceStatsByDateRangeAsync(Guid userId, DateTime startDate, DateTime endDate);

        // Property-Based Retrieval
        Task<PagedResultDto<InvoiceDto>> GetInvoicesByPropertyAsync(Guid propertyId, InvoiceFilterDto filter);
        Task<IEnumerable<InvoiceDto>> GetPropertyInvoicesByStatusAsync(Guid propertyId, string status);
        Task<IEnumerable<InvoiceDto>> GetPropertyInvoicesByTypeAsync(Guid propertyId, string type);
        Task<PropertyInvoiceStatsDto> GetPropertyInvoiceStatsAsync(Guid propertyId);
        Task<PropertyInvoiceStatsDto> GetPropertyInvoiceStatsByDateRangeAsync(Guid propertyId, DateTime startDate, DateTime endDate);
        Task<decimal> GetPropertyTotalSpentAsync(Guid propertyId);
        Task<IEnumerable<InvoiceDto>> GetPropertyInvoiceHistoryAsync(Guid propertyId);

        // Advanced Query Methods
        Task<PagedResultDto<InvoiceDto>> GetInvoicesByUserAndPropertyAsync(Guid userId, Guid propertyId, InvoiceFilterDto filter);
        Task<IEnumerable<InvoiceDto>> GetPendingInvoicesAsync(Guid? userId = null, Guid? propertyId = null);
        Task<IEnumerable<InvoiceDto>> GetOverdueInvoicesAsync(int daysOverdue = 30);
        Task<PagedResultDto<InvoiceDto>> SearchInvoicesAsync(string searchTerm, InvoiceFilterDto filter);

        // Status Management
        Task<bool> MarkInvoiceAsPaidAsync(Guid id, Guid userId);
        Task<bool> MarkInvoiceAsFailedAsync(Guid id, Guid userId, string reason);
        Task<bool> CancelInvoiceAsync(Guid id, Guid userId, string reason);
        Task<bool> UpdateInvoiceStatusAsync(Guid id, string status, Guid userId);

        // Invoice Items Management
        Task<IEnumerable<InvoiceItemDto>> GetInvoiceItemsAsync(Guid invoiceId);
        Task<InvoiceItemDto> AddInvoiceItemAsync(Guid invoiceId, CreateInvoiceItemDto dto, Guid userId);
        Task<bool> UpdateInvoiceItemAsync(Guid itemId, CreateInvoiceItemDto dto, Guid userId);
        Task<bool> DeleteInvoiceItemAsync(Guid itemId, Guid userId);

        // Property-Specific Invoice Management
        Task<InvoiceDto> CreatePropertyPostInvoiceAsync(Guid propertyId, Guid userId, bool isHighlighted = false, bool isAutoRenew = false);
        Task<InvoiceDto> CreateHighlightInvoiceAsync(Guid propertyId, Guid userId, decimal amount);
        Task<InvoiceDto> UpdatePropertyInvoiceAsync(Guid propertyId, Guid userId, bool isHighlighted = false, bool isAutoRenew = false);
        Task<InvoiceDto?> GetPropertyNewPostInvoiceAsync(Guid propertyId);
        Task<bool> HasPendingInvoicesAsync(Guid propertyId);
    }
}
