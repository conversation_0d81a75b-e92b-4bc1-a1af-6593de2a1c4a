﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RealEstate.Domain.Entities
{
    public class WalletTransaction : BaseEntity
    {

        [ForeignKey("AppUser")]
        public Guid UserId { get; set; }

        [Column(TypeName = "numeric(20,2)")]
        public decimal Amount { get; set; }

        [Required]
        public string Type { get; set; } 

        [Required]
        public string Description { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Required]
        public string Status { get; set; }

        [Required]
        public string? PaymentMethod { get; set; }

        public string? TransactionReference { get; set; }

        public string? ExternalPaymentReference { get; set; }

        public DateTime? ProcessedAt { get; set; }

        public string? FailureReason { get; set; }

        public virtual AppUser User { get; set; }
    }
}
