User Favorites and Wallet Management APIs Documentation
I. User Favorites APIs
1. Get User Favorites
Endpoint: GET /api/UserFavorites
Description: Retrieves all favorites for the authenticated user.
Request: No request body required.
Response:
[
  {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "propertyId": "7ea85f64-5717-4562-b3fc-2c963f66afa1",
    "createdAt": "2023-08-10T12:30:45.123Z"
  },
  {
    "id": "4fa85f64-5717-4562-b3fc-2c963f66afa7",
    "propertyId": "8ea85f64-5717-4562-b3fc-2c963f66afa2",
    "createdAt": "2023-08-15T09:20:30.456Z"
  }
]

2. Add to Favorites
Endpoint: POST /api/UserFavorites/add
Description: Adds a property to the user's favorites.
Request:
{
  "propertyId": "7ea85f64-5717-4562-b3fc-2c963f66afa1"
}
Response:
{
  "success": true
}

3. Remove from Favorites
Endpoint: DELETE /api/UserFavorites/remove/{propertyId}
Description: Removes a property from the user's favorites.
Example URL: /api/UserFavorites/remove/7ea85f64-5717-4562-b3fc-2c963f66afa1
Response:
{
  "success": true
}

4. Check Favorite Status
Endpoint: POST /api/UserFavorites/check
Description: Checks if properties are in the user's favorites.
Request:
{
  "propertyIds": [
    "7ea85f64-5717-4562-b3fc-2c963f66afa1",
    "8ea85f64-5717-4562-b3fc-2c963f66afa2",
    "9ea85f64-5717-4562-b3fc-2c963f66afa3"
  ]
}

Response:
[
  {
    "propertyId": "7ea85f64-5717-4562-b3fc-2c963f66afa1",
    "isFavorite": true
  },
  {
    "propertyId": "8ea85f64-5717-4562-b3fc-2c963f66afa2",
    "isFavorite": true
  },
  {
    "propertyId": "9ea85f64-5717-4562-b3fc-2c963f66afa3",
    "isFavorite": false
  }
]

5. Get Favorites Count
Endpoint: GET /api/UserFavorites/count
Description: Gets the total number of favorites for the user.
Response:
{
  "count": 2
}


Favorites Implementation
The favorites functionality uses a UserFavorite entity with a many-to-many relationship between users and properties. The system ensures that a user cannot favorite the same property multiple times.