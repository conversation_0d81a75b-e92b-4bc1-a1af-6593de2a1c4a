using System.ComponentModel;

namespace RealEstate.Domain.Common
{
    public static class EnumValues
    {
        public enum UserType
        {
            Seller,
            Buyer,
            Admin
        }

        public enum UserRoleCode
        {
            [Description("Super Mod")]
            SUPER_MOD,

            [Description("System Admin")]
            SYSTEM_ADMIN,

            [Description("Admin Duyệt Bài")]
            ADMIN_APPROVER,

            [Description("Admin Nội Dung")]
            ADMIN_CONTENT,

            [Description("Kế Toán Trưởng")]
            FINANCE_MANAGER,

            [Description("Kế Toán Viên")]
            FINANCE_STAFF
        }

        public enum PermissionCode
        {
            // Admin & User Management
            [Description("P_USER_MANAGE_FULL")]
            P_USER_MANAGE_FULL,
            [Description("P_VIEW_USER_MANAGEMENT_SCREEN")]
            P_VIEW_USER_MANAGEMENT_SCREEN,

            // Listing Management
            [Description("P_LISTING_APPROVE")]
            P_LISTING_APPROVE,
            [Description("P_LISTING_MANAGE")] // Quyền sửa/xóa tin đăng
            P_LISTING_MANAGE,
            [Description("P_LISTING_VIEW_PRICE")] // Quyền xem giá tin đăng
            P_LISTING_VIEW_PRICE,

            // News Content Management
            [Description("P_NEWS_MANAGE")]
            P_NEWS_MANAGE,
            [Description("P_VIEW_NEWS_EDITOR_SCREEN")]
            P_VIEW_NEWS_EDITOR_SCREEN,

            // Accounting & Finance
            [Description("P_ACCOUNTING_VIEW")]
            P_ACCOUNTING_VIEW,
            [Description("P_ACCOUNTING_MANAGE")] // Quyền quản lý các tác vụ kế toán (nếu có)
            P_ACCOUNTING_MANAGE,
            [Description("P_INVOICE_EXPORT_E")]
            P_INVOICE_EXPORT_E,
            [Description("P_REPORT_FINANCE_VIEW")]
            P_REPORT_FINANCE_VIEW,
            [Description("P_INVOICE_REQUEST_VIEW")]
            P_INVOICE_REQUEST_VIEW,

            // All access / Super Mod specific
            [Description("P_ALL_ACCESS")] // Có thể không cần policy riêng nếu Super Mod được kiểm tra bằng RoleCode
            P_ALL_ACCESS
        }

        public enum PropertyStatus
        {
            Draft,
            PendingApproval,
            Approved,
            RejectedByAdmin,
            RejectedDueToUnpaid,
            WaitingPayment,
            Expired,
            Sold
        }

        public enum PostType
        {
            Sale,
            Rent,
        }

        public enum PropertyType
        {
            can_ho,
            nha_pho,
            nha_tro,
        }

        public enum ContactRequestStatus
        {
            Pending, // Chưa xử lý.
            Read, //Đã liên hệ khách hàng.
        }

        public enum NotificationType
        {
            System, // thông báo hệ thống - All user có thể thấy
            Transaction, // thông báo giao dich
            Contact, // thông báo có khách liên hệ
            Promotion, // thông báo khuyến mãi từ hệ thống - all user thấy
            News, // tin tức mới từ hệ thống - all user thấy
            WalletUpdate, // thông báo nạp tiền vào ví
            CustomerMessage 
        }

        public enum TransactionType
        {
            TOP_UP,
            PAYMENT_POST,
            PAYMENT_HIGHLIGHT
        }

        public enum TransactionStatus
        {
            COMPLETED,
            PENDING,
            FAILED,
            CANCELLED
        }

        public enum InvoiceStatus
        {
            [Description("PENDING")]
            PENDING,
            [Description("COMPLETED")]
            COMPLETED,
            [Description("FAILED")]
            FAILED,
            [Description("CANCELLED")]
            CANCELLED
        }

        public enum InvoiceType
        {
            [Description("new_post")]
            NEW_POST,
            [Description("renew")]
            RENEW,
            [Description("highlight")]
            HIGHLIGHT
        }

        public enum InvoiceItemType
        {
            [Description("new_post")]
            NEW_POST,
            [Description("renew")]
            RENEW,
            [Description("highlight")]
            HIGHLIGHT
        }

        public enum PropertyEngagementEventType
        {
            [Description("view")]
            View,
            [Description("favorite")]
            Favorite,
            [Description("unfavorite")]
            Unfavorite,
            [Description("click_phone")]
            ClickPhone,
            [Description("chat")]
            Chat,
            [Description("contact")]
            Contact,
            [Description("share")]
            Share,
            [Description("search_impression")]
            SearchImpression,
            [Description("click_through")]
            ClickThrough,
            [Description("conversion")]
            Conversion,
            [Description("print")]
            Print,
            [Description("save_for_later")]
            SaveForLater,
            [Description("report_listing")]
            ReportListing,
            [Description("view_map")]
            ViewMap,
            [Description("view_gallery")]
            ViewGallery,
            [Description("view_floor_plan")]
            ViewFloorPlan,
            [Description("view_video")]
            ViewVideo
        }

        public const int UPDATE_DEFAULT_REMAINING_TIMES = 5;
    }
}
