﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    /// <summary>
    /// Controller for user management, dashboard, wallet, properties, transactions, ranking, and account operations.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, IUserDashboardService dashboardService, ILogger<UserController> logger)
        {
            _userService = userService;
            _dashboardService = dashboardService;
            _logger = logger;
        }

        /// <summary>
        /// Adds a role to a user.
        /// </summary>
        /// <param name="addUserRoleDto">The user role data.</param>
        /// <returns>True if the role was added successfully.</returns>
        [HttpPut("role")]
        public async Task<ActionResult<AddUserRoleDto>> AddUserRole(AddUserRoleDto addUserRoleDto)
        {
            _logger.LogInformation("[AddUserRole] Adding role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
            LogUserAction(_logger, "AddUserRole", addUserRoleDto);
            try
            {
                bool isOk = await _userService.AddUserRoleAsync(addUserRoleDto);

                if (isOk)
                {
                    _logger.LogInformation("[AddUserRole] Successfully added role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                }
                else
                {
                    _logger.LogWarning("[AddUserRole] Failed to add role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                }

                return Ok(isOk);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[AddUserRole] Error adding role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                return BadRequest(new { Message = "An error occurred while adding user role. Please try again later." });
            }
        }

        /// <summary>
        /// Retrieves user information by user ID.
        /// </summary>
        /// <param name="id">The user ID.</param>
        /// <returns>User information.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            _logger.LogInformation("[GetUser] Retrieving user information for user {UserId}", id);
            LogUserAction(_logger, "GetUser", new { TargetUserId = id });
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    _logger.LogWarning("[GetUser] User {UserId} not found", id);
                    return NotFound(new { Message = "User not found" });
                }

                _logger.LogInformation("[GetUser] Successfully retrieved user information for user {UserId}", id);
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetUser] Error retrieving user {UserId}", id);
                return StatusCode(500, new { Message = "An error occurred while retrieving user information. Please try again later." });
            }
        }

        /// <summary>
        /// Retrieves the dashboard for the authenticated user.
        /// </summary>
        /// <returns>User dashboard data.</returns>
        [HttpGet("dashboard")]
        public async Task<ActionResult<UserDashboardDto>> GetUserDashboard()
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetUserDashboard] Retrieving dashboard for user {UserId}", userId);
            LogUserAction(_logger, "GetUserDashboard");
            try
            {
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedDashboardAccess", "User attempted to access dashboard without valid authentication");
                    _logger.LogWarning("[GetUserDashboard] User not authenticated");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                var dashboard = await _dashboardService.GetUserDashboardAsync(userId.Value);

                _logger.LogInformation("[GetUserDashboard] Successfully retrieved dashboard for user {UserId}", userId.Value);
                return Ok(dashboard);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("[GetUserDashboard] Dashboard not found for user {UserId}: {Message}", userId, ex.Message);
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetUserDashboard] Error retrieving dashboard for user {UserId}", userId);
                return StatusCode(500, new { Message = "An error occurred while retrieving dashboard. Please try again later." });
            }
        }

        /// <summary>
        /// Retrieves wallet information for the authenticated user.
        /// </summary>
        /// <returns>User wallet information.</returns>
        [HttpGet("wallet")]
        public async Task<ActionResult<WalletInfoDto>> GetUserWallet()
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetUserWallet] Retrieving wallet info for user {UserId}", userId);
            try
            {
                if (!userId.HasValue)
                {
                    _logger.LogWarning("[GetUserWallet] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var walletInfo = await _dashboardService.GetUserWalletInfoAsync(userId.Value);
                _logger.LogInformation("[GetUserWallet] Successfully retrieved wallet info for user {UserId}", userId.Value);
                return Ok(walletInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetUserWallet] Error retrieving wallet info for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves property statistics for the authenticated user.
        /// </summary>
        /// <returns>User property statistics.</returns>
        [HttpGet("properties/stats")]
        public async Task<ActionResult<PropertyStatsDto>> GetUserPropertyStats()
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetUserPropertyStats] Retrieving property stats for user {UserId}", userId);
            try
            {
                if (!userId.HasValue)
                {
                    _logger.LogWarning("[GetUserPropertyStats] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var propertyStats = await _dashboardService.GetUserPropertyStatsAsync(userId.Value);
                _logger.LogInformation("[GetUserPropertyStats] Successfully retrieved property stats for user {UserId}", userId.Value);
                return Ok(propertyStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetUserPropertyStats] Error retrieving property stats for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves wallet transactions for the authenticated user.
        /// </summary>
        /// <param name="count">Number of transactions to retrieve.</param>
        /// <returns>List of wallet transactions.</returns>
        [HttpGet("transactions")]
        public async Task<ActionResult<List<WalletTransactionDto>>> GetUserTransactions([FromQuery] int count = 10)
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetUserTransactions] Retrieving {Count} transactions for user {UserId}", count, userId);
            try
            {
                if (!userId.HasValue)
                {
                    _logger.LogWarning("[GetUserTransactions] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var transactions = await _dashboardService.GetUserTransactionsAsync(userId.Value, count);
                _logger.LogInformation("[GetUserTransactions] Successfully retrieved transactions for user {UserId}", userId.Value);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetUserTransactions] Error retrieving transactions for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves member ranking information for the authenticated user.
        /// </summary>
        /// <returns>User member ranking information.</returns>
        [HttpGet("ranking")]
        public async Task<ActionResult<MemberRankingDto>> GetUserRanking()
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetUserRanking] Retrieving ranking info for user {UserId}", userId);
            try
            {
                if (!userId.HasValue)
                {
                    _logger.LogWarning("[GetUserRanking] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var rankingInfo = await _dashboardService.GetUserMemberRankingInfoAsync(userId.Value);
                _logger.LogInformation("[GetUserRanking] Successfully retrieved ranking info for user {UserId}", userId.Value);
                return Ok(rankingInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetUserRanking] Error retrieving ranking info for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves monthly spending data for the authenticated user.
        /// </summary>
        /// <param name="year">The year for which to retrieve spending data.</param>
        /// <returns>Monthly spending data.</returns>
        [HttpGet("spending/monthly")]
        public async Task<ActionResult> GetMonthlySpending([FromQuery] int year)
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetMonthlySpending] Retrieving monthly spending for user {UserId} in year {Year}", userId, year);
            try
            {
                if (!userId.HasValue)
                {
                    _logger.LogWarning("[GetMonthlySpending] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var spendingData = await _dashboardService.GetMonthlySpendingAsync(userId.Value, year);
                _logger.LogInformation("[GetMonthlySpending] Successfully retrieved monthly spending for user {UserId}", userId.Value);
                return Ok(spendingData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetMonthlySpending] Error retrieving monthly spending for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves property performance data for the authenticated user.
        /// </summary>
        /// <returns>Property performance data.</returns>
        [HttpGet("properties/performance")]
        public async Task<ActionResult> GetPropertyPerformance()
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetPropertyPerformance] Retrieving property performance for user {UserId}", userId);
            try
            {
                if (!userId.HasValue)
                {
                    _logger.LogWarning("[GetPropertyPerformance] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var performanceData = await _dashboardService.GetPropertyPerformanceAsync(userId.Value);
                _logger.LogInformation("[GetPropertyPerformance] Successfully retrieved property performance for user {UserId}", userId.Value);
                return Ok(performanceData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetPropertyPerformance] Error retrieving property performance for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Deactivates the authenticated user's account.
        /// </summary>
        /// <param name="deactivateUserDto">The deactivation data.</param>
        /// <returns>Status of the deactivation.</returns>
        [HttpPost("deactivate")]
        public async Task<ActionResult> DeactivateAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            var userId = GetUserId();
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("[DeactivateAccount] Invalid model state for account deactivation by user {UserId}: {ValidationErrors}",
                    userId, ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ModelState);
            }

            try
            {
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedDeactivationAttempt", "User attempted to deactivate account without valid authentication");
                    _logger.LogWarning("[DeactivateAccount] User not authenticated");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogWarning("[DeactivateAccount] User {UserId} is attempting to deactivate their account", userId.Value);
                LogUserAction(_logger, "DeactivateAccount");

                var result = await _userService.DeactivateUserAsync(userId.Value, deactivateUserDto);
                if (!result)
                {
                    _logger.LogWarning("[DeactivateAccount] Failed to deactivate account for user {UserId} - invalid credentials", userId.Value);
                    return BadRequest(new { Message = "Failed to deactivate account. Please check your password and try again." });
                }

                _logger.LogWarning("[DeactivateAccount] Account successfully deactivated for user {UserId}", userId.Value);
                return Ok(new { Message = "Your account has been deactivated successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DeactivateAccount] Error deactivating account for user {UserId}", userId);
                return StatusCode(500, new { Message = "An error occurred while deactivating account. Please try again later." });
            }
        }

        /// <summary>
        /// Permanently deletes the authenticated user's account.
        /// </summary>
        /// <param name="deactivateUserDto">The deletion data.</param>
        /// <returns>Status of the permanent deletion.</returns>
        [HttpDelete("permanent-delete")]
        public async Task<ActionResult> PermanentDeleteAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            var userId = GetUserId();
            try
            {
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedDeletionAttempt", "User attempted to permanently delete account without valid authentication");
                    _logger.LogWarning("[PermanentDeleteAccount] User not authenticated");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogCritical("[PermanentDeleteAccount] User {UserId} is attempting to permanently delete their account", userId.Value);
                LogUserAction(_logger, "PermanentDeleteAccount");

                var result = await _userService.PermanentDeleteUserAsync(userId.Value, deactivateUserDto);
                if (!result)
                {
                    _logger.LogWarning("[PermanentDeleteAccount] Failed to permanently delete account for user {UserId} - invalid credentials", userId.Value);
                    return BadRequest(new { Message = "Failed to delete account. Please check your password and try again." });
                }

                _logger.LogCritical("[PermanentDeleteAccount] Account permanently deleted for user {UserId}", userId.Value);
                return Ok(new { Message = "Your account and all associated data have been permanently deleted." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PermanentDeleteAccount] Error permanently deleting account for user {UserId}", userId);
                return StatusCode(500, new { Message = "An error occurred while deleting account. Please try again later." });
            }
        }

        /// <summary>
        /// Retrieves tax information for the authenticated user.
        /// </summary>
        /// <returns>User tax information.</returns>
        [HttpGet("tax-info")]
        public async Task<ActionResult<UserInvoiceInfoDto>> GetUserTaxInfo()
        {
            var userId = GetUserId();
            _logger.LogInformation("[GetUserTaxInfo] Retrieving tax info for user {UserId}", userId);
            try
            {
                if (!userId.HasValue)
                {
                    _logger.LogWarning("[GetUserTaxInfo] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var user = await _userService.GetUserByIdAsync(userId.Value);
                if (user == null)
                {
                    _logger.LogWarning("[GetUserTaxInfo] User not found for user {UserId}", userId.Value);
                    return NotFound("User not found");
                }

                _logger.LogInformation("[GetUserTaxInfo] Successfully retrieved tax info for user {UserId}", userId.Value);
                return Ok(new {
                    PersonalTaxCode = user.PersonalTaxCode,
                    InvoiceInfo = user.InvoiceInfo
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetUserTaxInfo] Error retrieving tax info for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates tax information for the authenticated user.
        /// </summary>
        /// <param name="taxInfoDto">The tax info update data.</param>
        /// <returns>Status of the update.</returns>
        [HttpPut("tax-info")]
        public async Task<ActionResult> UpdateUserTaxInfo([FromBody] UpdateUserTaxInfoDto taxInfoDto)
        {
            var userId = GetUserId();
            _logger.LogInformation("[UpdateUserTaxInfo] Updating tax info for user {UserId}", userId);
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("[UpdateUserTaxInfo] Invalid model state for user {UserId}", userId);
                    return BadRequest(ModelState); // Returns a 400 Bad Request with validation errors
                }

                if (!userId.HasValue)
                {
                    _logger.LogWarning("[UpdateUserTaxInfo] User not authenticated");
                    return Unauthorized("User not authenticated");
                }

                var result = await _userService.UpdateUserTaxInfoAsync(userId.Value, taxInfoDto);
                if (!result)
                {
                    _logger.LogWarning("[UpdateUserTaxInfo] Failed to update tax info for user {UserId}", userId.Value);
                    return BadRequest("Failed to update tax information.");
                }

                _logger.LogInformation("[UpdateUserTaxInfo] Tax information updated successfully for user {UserId}", userId.Value);
                return Ok(new { Message = "Tax information updated successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[UpdateUserTaxInfo] Error updating tax info for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
