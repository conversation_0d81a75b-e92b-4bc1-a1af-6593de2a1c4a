using System.ComponentModel.DataAnnotations;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class CreateInvoiceDto
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        public InvoiceType Type { get; set; }
        
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "Total amount must be greater than or equal to 0")]
        public int TotalAmount { get; set; }
        
        public string? Note { get; set; }
        
        public ICollection<CreateInvoiceItemDto> InvoiceItems { get; set; } = new List<CreateInvoiceItemDto>();
    }
}
